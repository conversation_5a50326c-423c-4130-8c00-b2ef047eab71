#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
还原的 getByteHash 函数
基于 IDA Pro 反编译的 libkeyinfo.so 中的 getByteHash 函数

原函数签名: char *__fastcall getByteHash(int a1, int a2, int a3, int a4, char *a5)
功能: 计算输入数据的SHA1哈希值，并将结果格式化为十六进制字符串
"""

import hashlib


def getByteHash(data, length=None):
    """
    还原的 getByteHash 函数

    根据IDA反编译代码分析:
    1. 函数首先检查输入数据是否为空 (if (!a3) return 0)
    2. 使用SHA1算法计算哈希值
    3. 将20字节的SHA1结果按4字节为一组，格式化为8位十六进制字符串
    4. 最终返回40字符的十六进制字符串

    参数:
        data: 要计算哈希的数据 (bytes 或 str)
        length: 数据长度 (可选，如果不提供则使用 len(data))

    返回:
        str: SHA1 哈希值的十六进制字符串表示 (40个字符)，如果输入为空则返回空字符串
    """
    # 对应原函数中的 if (!a3) return 0;
    if not data:
        return ""

    # 如果是字符串，转换为字节
    if isinstance(data, str):
        data = data.encode('utf-8')

    # 如果指定了长度，截取相应长度的数据
    if length is not None:
        data = data[:length]

    # 对应原函数中的 j_SHA1Reset(v12); j_SHA1Input(v12, a3, a4); j_SHA1Result(v12)
    sha1_hash = hashlib.sha1()
    sha1_hash.update(data)
    hash_digest = sha1_hash.digest()

    # 对应原函数中的循环: for (i = 0; i != 5; ++i)
    # 将20字节的哈希结果转换为十六进制字符串
    result = ""

    # 将20字节的哈希结果按4字节为一组处理（5组）
    for i in range(5):
        # 提取4字节 (对应 v9 = v12[i])
        start_idx = i * 4
        chunk = hash_digest[start_idx:start_idx + 4]

        # 转换为32位整数（大端序）
        value = int.from_bytes(chunk, byteorder='big', signed=False)

        # 对应原函数中的 sprintf(v11, "%08x", v9); strcat(a5, v11);
        # 格式化为8位十六进制字符串（不带0x前缀）
        result += f"{value:08x}"

    return result


def getByteHash_simple(data, length=None):
    """
    简化版本的实现，直接使用 hexdigest()

    参数:
        data: 要计算哈希的数据 (bytes 或 str)
        length: 数据长度 (可选)

    返回:
        str: SHA1 哈希值的十六进制字符串表示
    """
    if not data:
        return ""

    if isinstance(data, str):
        data = data.encode('utf-8')

    if length is not None:
        data = data[:length]

    return hashlib.sha1(data).hexdigest()


def test_getByteHash():
    """测试函数，验证还原的正确性"""
    print("=" * 60)
    print("测试 getByteHash 函数还原结果")
    print("=" * 60)

    # 测试用例
    test_cases = [
        (b"hello world", None, "测试基本字符串"),
        (b"test data", None, "测试另一个字符串"),
        (b"", None, "测试空数据"),
        ("中文测试", None, "测试中文字符串"),
        (b"a" * 100, 50, "测试长数据截取"),
        (b"The quick brown fox jumps over the lazy dog", None, "测试标准测试字符串"),
    ]

    for i, (test_data, length, description) in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {description}")
        print(f"输入数据: {test_data}")
        if length:
            print(f"指定长度: {length}")

        try:
            result1 = getByteHash(test_data, length)
            result2 = getByteHash_simple(test_data, length)

            print(f"还原函数结果: {result1}")
            print(f"简化版本结果: {result2}")
            print(f"结果一致性: {'✓' if result1 == result2 else '✗'}")

            # 验证长度
            if result1:
                print(f"结果长度: {len(result1)} 字符")
                if len(result1) != 40 and result1:
                    print("⚠️  警告: SHA1哈希结果应该是40个字符")

        except Exception as e:
            print(f"❌ 错误: {e}")

    print("\n" + "=" * 60)


def compare_with_original_logic():
    """
    模拟原函数的逻辑流程进行对比测试
    """
    print("\n模拟原函数逻辑流程:")
    print("-" * 40)

    test_data = b"hello world"
    print(f"测试数据: {test_data}")

    # 计算SHA1
    sha1_hash = hashlib.sha1(test_data)
    hash_digest = sha1_hash.digest()

    print(f"SHA1原始结果 (20字节): {hash_digest.hex()}")
    print(f"SHA1原始结果 (bytes): {list(hash_digest)}")

    # 按原函数逻辑处理
    print("\n按4字节分组处理:")
    result = ""
    for i in range(5):
        start_idx = i * 4
        chunk = hash_digest[start_idx:start_idx + 4]
        value = int.from_bytes(chunk, byteorder='big', signed=False)
        hex_str = f"{value:08x}"
        print(f"组 {i+1}: bytes={list(chunk)} -> int={value} -> hex={hex_str}")
        result += hex_str

    print(f"\n最终结果: {result}")
    print(f"标准hexdigest: {sha1_hash.hexdigest()}")
    print(f"结果一致: {'✓' if result == sha1_hash.hexdigest() else '✗'}")


if __name__ == "__main__":
    test_getByteHash()
    compare_with_original_logic()
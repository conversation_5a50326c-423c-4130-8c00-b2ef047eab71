import websocket
import time
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import base64

ws_url = "wss://score-p.599.com/scorepush/football"
key = b"777db0c19edfaace"
iv = b"9876543210599311"


def aes_cbc_decrypt(encrypted_data, key, iv):
    ciphertext = base64.b64decode(encrypted_data)
    cipher = AES.new(key, AES.MODE_CBC, iv=iv)
    decrypted_data = cipher.decrypt(ciphertext)
    plaintext = unpad(decrypted_data, AES.block_size)
    return plaintext


def sync_websocket_client():
    url = "wss://score-p.599.com/scorepush/football"

    # 准备Headers（注意：WebSocket的header格式和HTTP不同）
    headers = {
        "Origin": "https://www.599.com",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Pragma": "no-cache",
        "Cache-Control": "no-cache"
    }

    # 准备Cookies（需要转换成字符串）
    cookies = {
        'douyin_ads': 'S12wxUntJS',
        'Hm_lvt_b8167d9d4d6b87ad4f016f6096a48019': '**********',
        'HMACCOUNT': '4C68CBB363B8EA3C',
        'ads-tracker-baidu': 'xKcbUGs69xmovnoaBItJkG5CIlVEffMofD-KtaXLMH8YxvcUGImsVFynob..',
        'Hm_lpvt_b8167d9d4d6b87ad4f016f6096a48019': '**********',
    }
    cookie_str = "; ".join([f"{k}={v}" for k, v in cookies.items()])

    # 创建连接（关键修改点）
    ws = websocket.create_connection(
        url,
        header=headers,  # 传递标准WebSocket头
        cookie=cookie_str,  # 单独传递Cookies
        sslopt={"cert_reqs": 0}  # 等同于verify_ssl_cert=False
    )

    print(f"Connected to: {url}")

    try:
        # 发送消息
        send_data = '{"device":"pc","topic":"USER.topic.app.8"}'
        ws.send(send_data)

        # 接收消息
        while True:
            time.sleep(1)
            try:
                message = ws.recv()
                message = aes_cbc_decrypt(message, key, iv)
                print(f"Received: {message}")
            except websocket.WebSocketException as e:
                print(f"Error: {e}")
                break

    except Exception as e:
        print(f"Connection error: {e}")
    finally:
        ws.close()


if __name__ == "__main__":
    sync_websocket_client()
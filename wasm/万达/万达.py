import requests
import json
import time
import hashlib
from urllib.parse import urlencode


class WandaCinemaAPI:
    """万达影院API客户端"""
    
    BASE_URL = "https://cinema-api-prd-mx.wandafilm.com"
    REFERER = "https://m.wandacinemas.com/"
    SECRET_KEY = "FA425A3F9F5FFFC4389994548F83298776F8B46B752A83A6A798C6ED8FE8BFE1"
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(self._get_default_headers())
    
    def _get_default_headers(self):
        """获取默认请求头"""
        return {
            "accept": "application/json, text/javascript, */*; q=0.01",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "no-cache",
            "origin": "https://m.wandacinemas.com",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "referer": self.REFERER,
            "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "cross-site",
            "sec-fetch-storage-access": "active",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
    
    def _md5_hash(self, text):
        """生成MD5哈希值"""
        if isinstance(text, str):
            text = text.encode('utf-8')
        return hashlib.md5(text).hexdigest()
    
    def _generate_check_value(self, timestamp, api_path_with_params):
        """生成check验证值"""
        sign_string = f"Wanda1_3{self.SECRET_KEY}{timestamp}{api_path_with_params}"
        return self._md5_hash(sign_string)
    
    def _generate_mx_api(self, check_value, timestamp, mi_value=""):
        """生成mx-api头部值"""
        api_data = {
            "ver": "7.0.0",
            "sCode": "Wanda",
            "_mi_": mi_value,
            "width": 1280,
            "json": True,
            "cCode": "1_3",
            "check": check_value,
            "ts": timestamp,
            "heigth": 720,
            "appId": "3"
        }
        return json.dumps(api_data, separators=(',', ':'))
    
    def _build_api_path_with_params(self, endpoint, params):
        """构建带参数的API路径"""
        if params:
            query_string = urlencode(params, doseq=True)
            return f"{endpoint}?{query_string}"
        return endpoint
    
    def _make_request(self, endpoint, params=None, method="GET"):
        """发送API请求"""
        try:
            # 生成时间戳
            timestamp = int(time.time() * 1000)
            
            # 构建完整的API路径（用于签名）
            api_path_with_params = self._build_api_path_with_params(endpoint, params)
            
            # 生成验证值
            check_value = self._generate_check_value(timestamp, api_path_with_params)
            
            # 生成mx-api头部
            mx_api_value = self._generate_mx_api(check_value, timestamp)
            
            # 更新请求头
            self.session.headers.update({"mx-api": mx_api_value})
            
            # 构建完整URL
            url = f"{self.BASE_URL}{endpoint}"
            
            # 发送请求
            if method.upper() == "GET":
                response = self.session.get(url, params=params)
            elif method.upper() == "POST":
                response = self.session.post(url, json=params)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return None
        except Exception as e:
            print(f"未知错误: {e}")
            return None
    
    def get_cinemas_by_location(self, location_id, lon="0", lat="0"):
        """根据位置ID获取影院信息"""
        endpoint = "/cinema/by_locationid.api"
        params = {
            "locationId": str(location_id),
            "lon": str(lon),
            "lat": str(lat),
            "json": "true"
        }
        return self._make_request(endpoint, params)


def main():
    """主函数"""
    # 创建API客户端
    api = WandaCinemaAPI()
    
    # 获取位置ID为354的影院信息
    result = api.get_cinemas_by_location(location_id=354)
    
    if result:
        print("请求成功!")
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        print("请求失败!")


if __name__ == "__main__":
    main()
# getByteHash 函数还原分析

## 原函数信息
- **地址**: 0xa287c
- **大小**: 0xac (172字节)
- **函数签名**: `char *__fastcall getByteHash(int a1, int a2, int a3, int a4, char *a5)`

## 反编译代码分析

### 函数参数分析
根据反编译代码和汇编分析：
- `a1`, `a2`: 可能是上下文参数（未直接使用）
- `a3`: 输入数据指针
- `a4`: 数据长度
- `a5`: 输出缓冲区指针

### 核心逻辑流程

1. **输入验证**
   ```c
   if (!a3)
       return 0;
   ```
   如果输入数据为空，直接返回0

2. **SHA1计算**
   ```c
   j_SHA1Reset(v12);
   j_SHA1Input(v12, a3, a4);
   j_SHA1Result(v12);
   ```
   使用标准SHA1算法计算哈希值

3. **结果格式化**
   ```c
   for (i = 0; i != 5; ++i) {
       v9 = v12[i];
       memset(v11, 0, 64);
       sprintf(v11, "%08x", v9);
       strcat(a5, v11);
   }
   ```
   将20字节SHA1结果按4字节为一组，格式化为8位十六进制字符串

## Python还原实现

### 核心函数
```python
def getByteHash(data, length=None):
    # 输入验证
    if not data:
        return ""

    # 数据预处理
    if isinstance(data, str):
        data = data.encode('utf-8')
    if length is not None:
        data = data[:length]

    # SHA1计算
    sha1_hash = hashlib.sha1()
    sha1_hash.update(data)
    hash_digest = sha1_hash.digest()

    # 结果格式化
    result = ""
    for i in range(5):
        start_idx = i * 4
        chunk = hash_digest[start_idx:start_idx + 4]
        value = int.from_bytes(chunk, byteorder='big', signed=False)
        result += f"{value:08x}"

    return result
```

### 关键技术点

1. **字节序处理**: 使用大端序 (`byteorder='big'`) 来匹配原函数的处理方式
2. **格式化**: 使用 `%08x` 格式确保每组输出8位十六进制字符
3. **数据类型**: 处理字符串和字节数据的兼容性

## 测试验证

所有测试用例都通过，包括：
- ✅ 基本字符串测试
- ✅ 空数据处理
- ✅ 中文字符串
- ✅ 长度截取功能
- ✅ 与标准SHA1结果一致性验证

## 使用示例

```python
# 基本使用
hash_result = getByteHash(b"hello world")
print(hash_result)  # 输出: 2aae6c35c94fcfb415dbe95f408b9ce91ee846ed

# 字符串输入
hash_result = getByteHash("test string")

# 指定长度
hash_result = getByteHash(b"long data...", length=10)
```

## 汇编代码关键点分析

从汇编代码可以看出：
- 使用ARM架构指令
- 函数使用栈保护机制 (`__stack_chk_guard`)
- SHA1相关函数调用：`j_SHA1Reset`, `j_SHA1Input`, `j_SHA1Result`
- 循环处理5次，每次处理4字节数据

## 总结

成功还原了 `getByteHash` 函数的完整功能，该函数本质上是一个SHA1哈希计算器，将输入数据转换为40字符的十六进制字符串表示。还原的Python版本与原C函数在逻辑和输出上完全一致。

### 文件说明
- `getByteHash_restored.py`: 完整的Python还原实现，包含测试用例
- `getByteHash_analysis.md`: 详细的分析文档
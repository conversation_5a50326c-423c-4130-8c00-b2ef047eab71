"""
打包脚本 - 将Excel处理工具打包成可执行文件
使用PyInstaller进行打包
"""

import os
import subprocess
import sys

def install_requirements():
    """安装必要的依赖包"""
    requirements = [
        'pandas',
        'openpyxl',
        'pyinstaller'
    ]
    
    print("正在安装必要的依赖包...")
    for package in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    return True

def build_executable():
    """使用PyInstaller打包程序"""
    script_name = "表格处理.py"
    exe_name = "Excel责任销售数据拆分工具"
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',  # 打包成单个文件
        '--windowed',  # 不显示控制台窗口（可选）
        '--name', exe_name,  # 可执行文件名称
        '--add-data', f'{script_name};.',  # 添加脚本文件
        '--hidden-import', 'pandas',
        '--hidden-import', 'openpyxl',
        '--hidden-import', 'tkinter',
        script_name
    ]
    
    print(f"正在打包 {script_name}...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        subprocess.check_call(cmd)
        print(f"✅ 打包成功！")
        print(f"可执行文件位置: dist/{exe_name}.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Excel责任销售数据拆分工具 - 打包脚本")
    print("=" * 60)
    
    # 检查当前目录是否有表格处理.py文件
    if not os.path.exists("表格处理.py"):
        print("❌ 错误: 找不到 表格处理.py 文件")
        print("请确保在包含 表格处理.py 的目录中运行此脚本")
        input("按回车键退出...")
        return
    
    # 安装依赖
    if not install_requirements():
        print("❌ 依赖安装失败，无法继续打包")
        input("按回车键退出...")
        return
    
    # 打包程序
    if build_executable():
        print("\n🎉 打包完成！")
        print("使用说明：")
        print("1. 可执行文件位于 dist/ 目录中")
        print("2. 双击运行，会弹出文件选择对话框")
        print("3. 也可以通过命令行传入文件路径：")
        print("   Excel责任销售数据拆分工具.exe \"文件路径.xlsx\"")
    else:
        print("❌ 打包失败")
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()

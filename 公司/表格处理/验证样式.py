from openpyxl import load_workbook

def check_excel_styles(file_path):
    """检查Excel文件的样式信息"""
    wb = load_workbook(file_path)
    ws = wb.active
    
    print(f"检查文件: {file_path}")
    print(f"工作表名称: {ws.title}")
    print(f"最大行数: {ws.max_row}")
    print(f"最大列数: {ws.max_column}")
    
    # 检查前几行的样式
    print("\n前5行样式信息:")
    for row in range(1, min(6, ws.max_row + 1)):
        print(f"\n第{row}行:")
        for col in range(1, min(6, ws.max_column + 1)):
            cell = ws.cell(row=row, column=col)
            print(f"  列{col}: 值='{cell.value}' | 字体={cell.font.name if cell.font else 'None'} | 填充={cell.fill.patternType if cell.fill else 'None'}")

if __name__ == "__main__":
    print("=== 原始文件样式 ===")
    check_excel_styles("公司/表格处理/2025年6月特殊处理登记(1).xlsx")

    print("\n" + "="*50)
    print("=== 处理后文件样式 ===")
    check_excel_styles("公司/表格处理/2025年6月特殊处理登记_处理结果.xlsx")

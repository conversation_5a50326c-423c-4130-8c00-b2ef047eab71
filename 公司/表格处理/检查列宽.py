from openpyxl import load_workbook
from openpyxl.utils import get_column_letter

def check_column_widths(file_path):
    """检查Excel文件的列宽设置"""
    wb = load_workbook(file_path)
    ws = wb.active
    
    print(f"检查文件: {file_path}")
    print(f"工作表名称: {ws.title}")
    print(f"最大列数: {ws.max_column}")
    
    print("\n列宽信息:")
    for col_idx in range(1, min(13, ws.max_column + 1)):  # 检查前12列
        col_letter = get_column_letter(col_idx)
        
        # 获取列标题
        header_cell = ws.cell(row=1, column=col_idx)
        header_value = str(header_cell.value)[:30] + "..." if len(str(header_cell.value)) > 30 else str(header_cell.value)
        
        # 获取列宽
        if col_letter in ws.column_dimensions:
            width = ws.column_dimensions[col_letter].width
        else:
            width = "默认"
        
        print(f"  列{col_letter}({col_idx}): 宽度={width} | 标题='{header_value}'")

if __name__ == "__main__":
    print("=== 原始文件列宽 ===")
    check_column_widths("公司/表格处理/2025年6月特殊处理登记(1).xlsx")
    
    print("\n" + "="*60)
    print("=== 处理后文件列宽 ===")
    check_column_widths("公司/表格处理/2025年6月特殊处理登记_处理结果_v2.xlsx")

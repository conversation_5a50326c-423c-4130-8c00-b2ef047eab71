# Excel责任销售数据拆分工具

## 功能说明
将Excel文件中责任销售列的多人比例数据拆分成多行，并自动计算分摊利润。

### 处理前
```
责任销售姓名: "吴何梅27.59%，吴俊廷72.41%"
订单财报利润: -174.99
```

### 处理后
```
第1行: 吴何梅 | 27.59% | -48.28 (分摊利润)
第2行: 吴俊廷 | 72.41% | -126.71 (分摊利润)
```

## 功能特点
- ✅ 自动解析责任销售列中的姓名和比例
- ✅ 按比例拆分数据到多行
- ✅ 自动计算分摊利润
- ✅ 保持原始文件的样式和格式
- ✅ 在原文件中添加"明细"工作表
- ✅ 支持命令行和交互式使用

## 文件说明

### 核心文件
- `excel_processor.py` - 主程序文件（用于打包）
- `表格处理.py` - 带GUI的完整版本
- `打包.bat` - 一键打包脚本

### 辅助文件
- `build.py` - Python打包脚本
- `build_simple.bat` - 简化打包脚本
- `验证工作表.py` - 验证工作表结构
- `检查列宽.py` - 检查列宽设置
- `验证样式.py` - 验证样式复制

## 使用方法

### 方法1：直接运行Python脚本
```bash
python excel_processor.py "文件路径.xlsx"
```

### 方法2：交互式运行
```bash
python excel_processor.py
# 然后输入文件路径
```

### 方法3：打包成可执行文件
1. 运行 `打包.bat`
2. 在 `dist` 目录中找到 `Excel责任销售数据拆分工具.exe`
3. 双击运行或拖拽Excel文件到exe上

## 打包步骤

### 自动打包（推荐）
双击运行 `打包.bat` 文件，脚本会自动：
1. 检查Python环境
2. 安装必要依赖
3. 使用PyInstaller打包
4. 生成可执行文件

### 手动打包
```bash
# 1. 安装依赖
pip install pandas openpyxl pyinstaller

# 2. 打包
pyinstaller --onefile --console --name "Excel责任销售数据拆分工具" excel_processor.py
```

## 系统要求
- Python 3.6+
- pandas
- openpyxl
- pyinstaller（仅打包时需要）

## 输出结果
程序会在原Excel文件中添加一个名为"明细"的新工作表，包含：
- 原始数据的所有列
- 新增"责任比例"列
- 新增"分摊利润"列
- 拆分后的多行数据

## 注意事项
1. 处理前请备份原始文件
2. 确保Excel文件未被其他程序占用
3. 责任销售列应为第6列（F列）
4. 比例格式应为：姓名+百分比，如"张三25.5%"
5. 多个销售人员用逗号分隔

## 错误处理
- 文件不存在：检查文件路径是否正确
- 文件被占用：关闭Excel后重试
- 格式错误：检查责任销售列的数据格式

## 版本历史
- v1.0: 基础功能实现
- v1.1: 添加样式复制
- v1.2: 优化列宽处理
- v1.3: 添加命令行支持和打包功能

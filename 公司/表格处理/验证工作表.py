from openpyxl import load_workbook

def check_worksheets(file_path):
    """检查Excel文件中的所有工作表"""
    wb = load_workbook(file_path)
    
    print(f"检查文件: {file_path}")
    print(f"工作表数量: {len(wb.sheetnames)}")
    print(f"工作表列表: {wb.sheetnames}")
    
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        print(f"\n工作表 '{sheet_name}':")
        print(f"  - 最大行数: {ws.max_row}")
        print(f"  - 最大列数: {ws.max_column}")
        
        # 显示前几个单元格的内容
        if ws.max_row > 0:
            first_row = []
            for col in range(1, min(6, ws.max_column + 1)):
                cell_value = ws.cell(row=1, column=col).value
                if cell_value:
                    first_row.append(str(cell_value)[:20] + "..." if len(str(cell_value)) > 20 else str(cell_value))
                else:
                    first_row.append("None")
            print(f"  - 第一行内容: {first_row}")

if __name__ == "__main__":
    check_worksheets("公司/表格处理/2025年6月特殊处理登记(1).xlsx")

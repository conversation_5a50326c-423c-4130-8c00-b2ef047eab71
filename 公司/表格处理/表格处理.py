import pandas as pd
import re
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Font, PatternFill, Border, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import copy
import sys
import os
from tkinter import filedialog, messagebox
import tkinter as tk

def parse_responsibility_sales(sales_str):
    """
    解析责任销售字符串，提取姓名和比例
    例如: "吴何梅27.59%，吴俊廷72.41%" -> [("吴何梅", 0.2759), ("吴俊廷", 0.7241)]
    """
    if pd.isna(sales_str) or sales_str.strip() == "":
        return []
    
    # 使用正则表达式匹配姓名和百分比
    pattern = r'([^，,\d%]+?)(\d+\.?\d*)%'
    matches = re.findall(pattern, sales_str)
    
    result = []
    for name, percentage in matches:
        name = name.strip()
        percentage = float(percentage) / 100
        result.append((name, percentage))
    
    return result

def copy_cell_style(source_cell, target_cell):
    """复制单元格样式"""
    if source_cell.font:
        target_cell.font = copy.copy(source_cell.font)
    if source_cell.fill:
        target_cell.fill = copy.copy(source_cell.fill)
    if source_cell.border:
        target_cell.border = copy.copy(source_cell.border)
    if source_cell.alignment:
        target_cell.alignment = copy.copy(source_cell.alignment)
    if source_cell.number_format:
        target_cell.number_format = source_cell.number_format

def process_excel_data_with_style(input_file):
    """
    处理Excel数据，将F列的责任销售信息拆分成多行，并在原文件中添加"明细"工作表
    """
    # 使用openpyxl加载原始工作簿
    source_wb = load_workbook(input_file)
    source_ws = source_wb.active

    # 读取Excel文件数据
    df = pd.read_excel(input_file)

    # 获取列名
    columns = df.columns.tolist()

    # 找到责任销售列的索引（假设是第6列，索引为5）
    responsibility_sales_col_idx = 5  # F列
    responsibility_sales_col = columns[responsibility_sales_col_idx]

    # 创建新的列名列表
    new_columns = columns.copy()
    # 在责任销售列后插入新的责任比例列
    new_columns.insert(responsibility_sales_col_idx + 1, "责任比例")
    # 在订单财报利润列后插入分摊利润列
    profit_col_idx = new_columns.index("订单财报利润")
    new_columns.insert(profit_col_idx + 1, "分摊利润")

    # 创建新的数据列表和行映射
    new_data = []
    row_mapping = []  # 记录新行对应的原始行号

    for index, row in df.iterrows():
        # 获取责任销售信息
        sales_info = row[responsibility_sales_col]

        # 解析责任销售信息
        parsed_sales = parse_responsibility_sales(str(sales_info))

        if not parsed_sales:
            # 如果没有解析到销售信息，保持原行
            new_row = row.tolist()
            new_row.insert(responsibility_sales_col_idx + 1, "")  # 责任比例
            new_row.insert(profit_col_idx + 1, "")  # 分摊利润
            new_data.append(new_row)
            row_mapping.append(index + 2)  # Excel行号从2开始（跳过标题行）
        else:
            # 获取订单财报利润
            order_profit = row["订单财报利润"]
            if pd.isna(order_profit):
                order_profit = 0

            # 为每个销售人员创建一行
            for name, percentage in parsed_sales:
                new_row = row.tolist()

                # 更新责任销售姓名为单个人员
                new_row[responsibility_sales_col_idx] = name

                # 插入责任比例
                percentage_str = f"{percentage:.2%}"
                new_row.insert(responsibility_sales_col_idx + 1, percentage_str)

                # 计算分摊利润
                allocated_profit = order_profit * percentage
                new_row.insert(profit_col_idx + 1, round(allocated_profit, 2))

                new_data.append(new_row)
                row_mapping.append(index + 2)  # Excel行号从2开始（跳过标题行）

    # 创建新的DataFrame
    new_df = pd.DataFrame(new_data, columns=new_columns)

    # 在原工作簿中创建新的工作表
    if "明细" in source_wb.sheetnames:
        # 如果"明细"工作表已存在，删除它
        del source_wb["明细"]

    target_ws = source_wb.create_sheet("明细")

    # 简化的列宽复制
    from openpyxl.utils import get_column_letter

    # 直接按照新列结构设置列宽
    for new_col_idx, column_name in enumerate(new_columns, 1):
        target_col_letter = get_column_letter(new_col_idx)

        if column_name == "责任比例":
            # 责任比例列使用固定宽度
            target_ws.column_dimensions[target_col_letter].width = 13
        elif column_name == "分摊利润":
            # 分摊利润列使用固定宽度
            target_ws.column_dimensions[target_col_letter].width = 13
        else:
            # 其他列从原始文件复制宽度
            original_col_idx = columns.index(column_name) + 1 if column_name in columns else None
            if original_col_idx:
                source_col_letter = get_column_letter(original_col_idx)
                if source_col_letter in source_ws.column_dimensions:
                    target_ws.column_dimensions[target_col_letter].width = source_ws.column_dimensions[source_col_letter].width
                else:
                    target_ws.column_dimensions[target_col_letter].width = 15
            else:
                target_ws.column_dimensions[target_col_letter].width = 15

    # 写入标题行并复制样式
    for col_idx, column_name in enumerate(new_columns, 1):
        target_cell = target_ws.cell(row=1, column=col_idx, value=column_name)

        # 找到对应的原始列索引来复制样式
        if col_idx <= len(columns):
            source_cell = source_ws.cell(row=1, column=col_idx)
            copy_cell_style(source_cell, target_cell)
        elif col_idx == responsibility_sales_col_idx + 2:  # 责任比例列
            source_cell = source_ws.cell(row=1, column=responsibility_sales_col_idx + 1)
            copy_cell_style(source_cell, target_cell)
        elif col_idx == profit_col_idx + 2:  # 分摊利润列
            source_cell = source_ws.cell(row=1, column=profit_col_idx + 1)
            copy_cell_style(source_cell, target_cell)

    # 写入数据行并复制样式
    for new_row_idx, (data_row, source_row_idx) in enumerate(zip(new_data, row_mapping), 2):
        for col_idx, value in enumerate(data_row, 1):
            target_cell = target_ws.cell(row=new_row_idx, column=col_idx, value=value)

            # 找到对应的原始列索引来复制样式
            if col_idx <= len(columns):
                source_cell = source_ws.cell(row=source_row_idx, column=col_idx)
                copy_cell_style(source_cell, target_cell)
            elif col_idx == responsibility_sales_col_idx + 2:  # 责任比例列
                source_cell = source_ws.cell(row=source_row_idx, column=responsibility_sales_col_idx + 1)
                copy_cell_style(source_cell, target_cell)
            elif col_idx == profit_col_idx + 2:  # 分摊利润列
                source_cell = source_ws.cell(row=source_row_idx, column=profit_col_idx + 1)
                copy_cell_style(source_cell, target_cell)



    # 保存工作簿（覆盖原文件）
    source_wb.save(input_file)
    print(f"处理完成，已在原文件中添加'明细'工作表: {input_file}")

    return new_df


def select_file():
    """使用文件对话框选择Excel文件"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    file_path = filedialog.askopenfilename(
        title="选择要处理的Excel文件",
        filetypes=[
            ("Excel文件", "*.xlsx"),
            ("Excel文件", "*.xls"),
            ("所有文件", "*.*")
        ]
    )

    root.destroy()
    return file_path

def main():
    """主函数"""
    print("=" * 60)
    print("Excel责任销售数据拆分工具")
    print("功能：将责任销售列中的多人比例数据拆分成多行")
    print("=" * 60)

    # 获取输入文件路径
    input_file = None

    # 检查命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
        if not os.path.exists(input_file):
            print(f"错误: 文件 '{input_file}' 不存在")
            input_file = None

    # 如果没有命令行参数或文件不存在，使用文件选择对话框
    if not input_file:
        print("请选择要处理的Excel文件...")
        input_file = select_file()

        if not input_file:
            print("未选择文件，程序退出。")
            input("按回车键退出...")
            return

    print(f"选择的文件: {input_file}")

    try:
        # 处理Excel文件
        print("正在处理文件，请稍候...")
        result_df = process_excel_data_with_style(input_file)

        print(f"\n✅ 处理完成！")
        print(f"文件: {input_file}")
        print(f"已添加'明细'工作表")
        print(f"\n📊 处理结果统计:")
        print(f"- 总共处理了 {len(result_df)} 行数据")
        print(f"- 新增了'责任比例'和'分摊利润'列")
        print(f"- 保持了原始文件的样式和格式")

        print(f"\n📋 处理结果预览（前10行）:")
        print(result_df.head(10).to_string(index=False))

        # 显示成功消息框
        root = tk.Tk()
        root.withdraw()
        messagebox.showinfo(
            "处理完成",
            f"Excel文件处理完成！\n\n文件: {os.path.basename(input_file)}\n已添加'明细'工作表\n总共处理了 {len(result_df)} 行数据"
        )
        root.destroy()

    except FileNotFoundError:
        error_msg = f"错误: 找不到文件 '{input_file}'"
        print(error_msg)
        messagebox.showerror("文件错误", error_msg)
    except PermissionError:
        error_msg = f"错误: 文件 '{input_file}' 被占用，请关闭Excel后重试"
        print(error_msg)
        messagebox.showerror("文件被占用", error_msg)
    except Exception as e:
        error_msg = f"处理过程中出现错误: {str(e)}"
        print(error_msg)
        messagebox.showerror("处理错误", error_msg)

    input("按回车键退出...")

if __name__ == "__main__":
    main()

@echo off
chcp 65001
echo ============================================================
echo Excel责任销售数据拆分工具 - 打包脚本
echo ============================================================

echo 正在检查Python环境...
python --version
if errorlevel 1 (
    echo 错误：未找到Python环境
    pause
    exit /b 1
)

echo.
echo 正在安装必要的依赖包...
pip install pandas openpyxl pyinstaller

echo.
echo 正在打包程序...
pyinstaller --onefile --console --name "Excel责任销售数据拆分工具" excel_processor.py

if exist "dist\Excel责任销售数据拆分工具.exe" (
    echo.
    echo ✅ 打包成功！
    echo 可执行文件位于: dist\Excel责任销售数据拆分工具.exe
    echo.
    echo 使用方法：
    echo 1. 双击运行程序
    echo 2. 输入Excel文件的完整路径
    echo 3. 或者将Excel文件拖拽到exe文件上
    echo.
) else (
    echo ❌ 打包失败
)

pause

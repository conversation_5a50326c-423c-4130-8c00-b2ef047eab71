Excel责任销售数据拆分工具 - 使用说明
===========================================

【功能】
将Excel文件中责任销售列的多人比例数据拆分成多行，并自动计算分摊利润。

【快速开始】
1. 双击运行"打包.bat"进行一键打包
2. 打包完成后，在dist目录找到"Excel责任销售数据拆分工具.exe"
3. 双击运行exe文件，输入Excel文件路径即可

【使用方法】
方法1：双击exe文件，然后输入Excel文件的完整路径
方法2：将Excel文件拖拽到exe文件上
方法3：命令行运行：Excel责任销售数据拆分工具.exe "文件路径.xlsx"

【处理效果】
原始数据：
- 责任销售：吴何梅27.59%，吴俊廷72.41%
- 订单财报利润：-174.99

处理后：
- 第1行：吴何梅 | 27.59% | -48.28
- 第2行：吴俊廷 | 72.41% | -126.71

【输出结果】
程序会在原Excel文件中添加"明细"工作表，包含拆分后的详细数据。

【注意事项】
1. 处理前请备份原始文件
2. 确保Excel文件未被占用
3. 责任销售数据格式：姓名+百分比，多人用逗号分隔

【技术支持】
如有问题，请检查：
- Python环境是否正确安装
- Excel文件路径是否正确
- 文件是否被其他程序占用

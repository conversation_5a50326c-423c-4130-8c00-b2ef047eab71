# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/26 11:46
@Auth ： Tianshilin
@File ：4px_login.py
@IDE ：PyCharm
"""
import re
import pymysql
import requests
import random
import math
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64
import logging
from typing import Optional, Tuple, Dict, Any

# 配置日志格式，包括时间信息
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',  # 包含时间、日志级别和日志消息
    datefmt='%Y-%m-%d %H:%M:%S',  # 时间格式
)

class CookieUtils:
    """Cookie工具类"""

    @staticmethod
    def get_cookie_by_domain(session, cookie_name: str, domain: str) -> Optional[str]:
        """
        根据域名获取特定的cookie值

        Args:
            session: requests.Session对象
            cookie_name: cookie名称
            domain: 域名

        Returns:
            cookie值，如果未找到返回None
        """
        for cookie in session.cookies:
            if cookie.name == cookie_name and domain in cookie.domain:
                return cookie.value
        return None

    @staticmethod
    def get_session_cookie_for_api(session) -> Optional[str]:
        """
        获取order-fulfillment-api.4px.com域名下的SESSION cookie

        Args:
            session: requests.Session对象

        Returns:
            SESSION cookie值，如果未找到返回None
        """
        return CookieUtils.get_cookie_by_domain(session, 'SESSION', 'order-fulfillment-api.4px.com')


def generate_random_string(n=None):
    """
    生成随机字符串的函数
    参数:
        n: 字符串长度，如果为None则默认为8
    返回:
        随机生成的字符串
    """
    chars = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K',
             'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']

    if n is None:
        n = 8

    res = ""
    for i in range(n):
        # 模拟JavaScript的Math.ceil(Math.random()*35)
        id = math.ceil(random.random() * 35)
        res += chars[id]

    return res


def encrypt(the_key, password):
    """
    AES加密函数
    :param the_key: 密钥
    :param password: 密码(原文)
    :return: 加密后的字符串
    """
    # 将密钥和密码转换为字节
    key = the_key.encode('utf-8')
    plaintext = password.encode('utf-8')

    # 确保密钥长度为16、24或32字节（对应AES-128、AES-192、AES-256）
    if len(key) < 16:
        key = key.ljust(16, b'\0')  # 如果密钥长度不足16字节，用0填充
    elif len(key) > 32:
        key = key[:32]  # 如果密钥长度超过32字节，截取前32字节
    elif 16 < len(key) <= 24:
        key = key.ljust(24, b'\0')
    elif 24 < len(key) <= 32:
        key = key.ljust(32, b'\0')

    # 创建AES加密器，使用ECB模式
    cipher = AES.new(key, AES.MODE_ECB)

    # 使用PKCS7填充
    padded_data = pad(plaintext, AES.block_size)

    # 进行加密
    encrypted = cipher.encrypt(padded_data)

    # 返回base64编码的结果（与JS的CryptoJS行为一致）
    return base64.b64encode(encrypted).decode('utf-8')

def login_4px(username,password):
    session = requests.session()

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'priority': 'u=0, i',
        'referer': 'https://b.4px.com/',
        'sec-ch-ua': '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-site',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0',
    }

    response = session.get('https://sso.4px.com/login?service=https://b.4px.com/cas', headers=headers)
    # 更精确的正则表达式
    match = re.search(r'<input\s+type="hidden"\s+name="execution"\s+value="([^"]+)"', response.text)
    execution_value = match.group(1)
    key = generate_random_string(16)
    password_data = encrypt(key,password)
    cookies = {
        'SESSION': response.cookies['SESSION'],
        'loginTypeIndexName': '2',
    }


    data = {
        'tenantCode': '4PX',
        'username': username,
        'imgverifycode': '',
        'lt': '',
        'execution': execution_value,
        '_eventId': 'submit',
        'key': key,
        'password': password_data,
    }

    response_2 = session.post('https://sso.4px.com/login?service=https://b.4px.com/cas', headers=headers,cookies=cookies,data=data)
    tgt_cn = session.cookies.get_dict()['tgt_cn']

    data = {
        'service': 'https://order-fulfillment-api.4px.com/cas',
    }

    response_3 = session.post(
        f'https://sso.4px.com/v1/tickets/{tgt_cn}',
        headers=headers,
        data=data,
    )

    params = {
        'ticket': response_3.text,
    }

    response_4 = session.get('https://order-fulfillment-api.4px.com/cas', params=params,headers=headers)

    print(response_4.cookies)
    session_value = CookieUtils.get_cookie_by_domain(session, 'SESSION', 'b.4px.com')
    sessionId = CookieUtils.get_cookie_by_domain(session, 'SESSION', 'order-fulfillment-api.4px.com')


    return session_value,sessionId

def get_token():
    # 连接到数据库
    connection = pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,  # 启用自动提交
        charset='utf8mb4'
    )
    username = "13434759827"
    password = "Se0908$Profich"
    session_value,sessionId = login_4px(username,password)
    with connection.cursor() as cursor:
        token_str = session_value+"---"+sessionId
        # 定义 UPDATE 语句
        sql = """
                       UPDATE sgm_system_config
                       SET value = %s
                       WHERE `key` = %s
                       """
        # 执行 UPDATE 语句
        cursor.execute(sql, (f"\"{token_str}\"", "4px_token"))
        logging.info(f"更新cookie session_value:{token_str}")

def schedule_job():
    logging.info("========4PX程序开始执行=====")
    get_token()
    logging.info("========4PX程序执行结束=====")


if __name__ == '__main__':
    schedule_job()

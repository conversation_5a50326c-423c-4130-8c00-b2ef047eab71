# 反爬虫优化指南

本文档详细说明了沃尔玛数据爬取程序的反爬虫优化功能。

## 主要优化功能

### 1. 智能请求头生成
- **随机User-Agent**: 从最新Chrome版本池中随机选择
- **动态请求头**: 随机生成真实浏览器头部信息
- **概率性头部**: 按概率添加可选头部，模拟真实浏览器行为

### 2. 反爬虫检测
程序能自动检测以下反爬虫机制：
- **PerimeterX**: 检测PX相关的阻止响应
- **Cloudflare**: 检测CF防护页面
- **人机验证**: 检测验证码页面
- **访问拒绝**: 检测403/禁止访问响应
- **重定向阻止**: 检测重定向到验证页面

### 3. 智能延迟策略
- **人类行为模拟**: 随机延迟2-8秒，模拟真实用户浏览
- **请求间隔**: 每次请求前1-4秒随机延迟
- **错误处理延迟**: 遇到反爬虫时增加10-30秒延迟

### 4. 会话管理
- **持久会话**: 使用requests.Session保持连接
- **会话轮换**: 每50个请求自动更换会话
- **连接复用**: 减少TCP连接开销

## 配置说明

### 延迟配置 (DELAY_CONFIG)
```python
{
    'min_delay': 2.0,           # 最小延迟（秒）
    'max_delay': 8.0,           # 最大延迟（秒）
    'request_delay_min': 1.0,   # 请求前最小延迟
    'request_delay_max': 4.0,   # 请求前最大延迟
    'error_delay_min': 10.0,    # 错误后最小延迟
    'error_delay_max': 30.0,    # 错误后最大延迟
}
```

### 请求头配置 (HEADERS_CONFIG)
```python
{
    'referer_probability': 0.7,     # 添加referer的概率
    'downlink_probability': 0.6,    # 添加downlink的概率
    'priority_probability': 0.5,    # 添加priority的概率
    'optional_headers_probability': 0.3,  # 添加可选头部的概率
}
```

### 反爬虫检测配置 (ANTI_BOT_CONFIG)
```python
{
    'max_retries': 3,           # 最大重试次数
    'retry_delay_min': 15.0,    # 重试最小延迟
    'retry_delay_max': 45.0,    # 重试最大延迟
}
```

## 使用方法

### 1. 测试请求头生成
```bash
python test_headers.py
```

### 2. 查看配置信息
```bash
python anti_bot_config.py
```

### 3. 运行优化后的爬虫
```bash
# 立即执行一次
python 沃尔玛.py --once

# 启动定时任务
python 沃尔玛.py --schedule
```

## 监控和调试

### 1. 日志监控
程序会记录以下信息：
- 使用的User-Agent
- 请求延迟时间
- 反爬虫检测结果
- 会话创建和关闭

### 2. Redis日志
反爬虫检测信息会存储到Redis中：
- `anti_bot_log`: 反爬虫检测日志
- `error_412_log`: 412错误日志
- `error_403_log`: 403错误日志
- `error_429_log`: 429错误日志

### 3. 调试模式
在`anti_bot_config.py`中设置：
```python
DEBUG_CONFIG = {
    'log_headers': True,        # 记录请求头
    'log_response_length': True, # 记录响应长度
    'save_blocked_responses': True,  # 保存被阻止的响应
}
```

## 常见问题处理

### 1. 频繁遇到PerimeterX阻止
**解决方案**:
- 增加延迟时间
- 更换User-Agent池
- 减少请求频率

### 2. 412 Precondition Failed错误
**解决方案**:
- 检查请求头格式
- 更新Chrome版本号
- 增加请求间隔

### 3. 403 Forbidden错误
**解决方案**:
- 更换User-Agent
- 检查IP是否被封
- 增加随机延迟

### 4. 429 Too Many Requests错误
**解决方案**:
- 大幅增加延迟时间
- 减少并发请求
- 考虑使用代理

## 性能优化建议

### 1. 延迟设置
- 生产环境建议延迟3-10秒
- 测试环境可适当减少延迟
- 根据目标网站调整延迟策略

### 2. 会话管理
- 适当增加每会话请求数
- 定期清理无效会话
- 监控会话成功率

### 3. 错误处理
- 设置合理的重试次数
- 实现指数退避策略
- 记录详细错误信息

## 更新和维护

### 1. User-Agent更新
定期更新Chrome版本号列表：
```python
# 在anti_bot_config.py中更新
'chrome_versions': [
    "134.0.6847.45",  # 新版本
    "135.0.6858.23",  # 新版本
    # ...
]
```

### 2. 检测模式更新
根据目标网站的变化更新检测模式：
```python
'detection_patterns': {
    'new_protection': ['new_pattern1', 'new_pattern2'],
    # ...
}
```

### 3. 配置调优
根据实际运行情况调整配置参数，提高成功率和效率。

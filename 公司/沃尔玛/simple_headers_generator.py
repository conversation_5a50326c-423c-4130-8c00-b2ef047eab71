#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简洁请求头生成器
专门用于生成简洁但有效的请求头，避免过度复杂化
"""

import random

def generate_walmart_headers():
    """
    生成专门用于沃尔玛的简洁请求头
    
    返回:
        dict: 简洁的请求头字典
    """
    # 最新Chrome版本池（精选稳定版本）
    chrome_versions = [
        "130.0.6723.58", "130.0.6723.69", "130.0.6723.70", "130.0.6723.91",
        "131.0.6778.69", "131.0.6778.85", "131.0.6778.86", "131.0.6778.108",
        "132.0.6834.83", "132.0.6834.110", "132.0.6834.159",
        "133.0.6835.57", "133.0.6835.106", "134.0.6847.45",
        "135.0.6858.23", "136.0.6869.15", "137.0.6880.45",
        "138.0.6891.23", "139.0.6902.15"
    ]
    
    # 语言设置（常用配置）
    languages = [
        "zh-CN,zh;q=0.9",
        "en-US,en;q=0.9", 
        "zh-CN,zh;q=0.9,en;q=0.8",
        "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7"
    ]
    
    # Windows版本
    windows_versions = ["10.0", "11.0"]
    
    # 随机选择
    chrome_version = random.choice(chrome_versions)
    language = random.choice(languages)
    windows_version = random.choice(windows_versions)
    
    # 生成User-Agent
    user_agent = f"Mozilla/5.0 (Windows NT {windows_version}; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"
    
    # 基础请求头（最简洁有效的配置）
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': language,
        'cache-control': 'no-cache',
        'user-agent': user_agent,
    }
    
    # 80%概率添加referer（模拟从沃尔玛首页进入）
    if random.random() < 0.8:
        headers['referer'] = 'https://www.walmart.com/'
    
    return headers

def get_sample_headers():
    """
    获取5组示例请求头
    
    返回:
        list: 包含5组请求头的列表
    """
    samples = []
    for i in range(5):
        headers = generate_walmart_headers()
        samples.append({
            'index': i + 1,
            'headers': headers
        })
    return samples

def print_headers_code(headers):
    """
    打印请求头的Python代码格式
    
    参数:
        headers (dict): 请求头字典
    """
    print("headers = {")
    for key, value in headers.items():
        print(f"    '{key}': '{value}',")
    print("}")

def main():
    """主函数 - 生成并显示示例请求头"""
    print("=" * 60)
    print("沃尔玛简洁请求头生成器")
    print("=" * 60)
    
    # 生成5组示例
    samples = get_sample_headers()
    
    for sample in samples:
        print(f"\n# 示例 {sample['index']} - 简洁请求头")
        print_headers_code(sample['headers'])
    
    print("\n" + "=" * 60)
    print("使用说明:")
    print("1. 直接复制上述任一组请求头到您的代码中")
    print("2. 或者调用 generate_walmart_headers() 函数动态生成")
    print("3. 这些请求头已经过优化，适合沃尔玛网站")
    print("=" * 60)

if __name__ == '__main__':
    main()

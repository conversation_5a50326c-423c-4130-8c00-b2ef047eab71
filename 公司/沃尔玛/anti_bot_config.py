#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反爬虫配置文件
"""

# 请求延迟配置
DELAY_CONFIG = {
    'min_delay': 2.0,           # 最小延迟（秒）
    'max_delay': 8.0,           # 最大延迟（秒）
    'request_delay_min': 1.0,   # 请求前最小延迟
    'request_delay_max': 4.0,   # 请求前最大延迟
    'error_delay_min': 10.0,    # 错误后最小延迟
    'error_delay_max': 30.0,    # 错误后最大延迟
}

# 会话管理配置
SESSION_CONFIG = {
    'max_requests_per_session': 50,  # 每个会话最大请求数
    'session_timeout': 30,           # 会话超时时间（秒）
}

# User-Agent配置
USER_AGENT_CONFIG = {
    'fail_threshold': 3,  # User-Agent失败阈值
    'chrome_versions': [
        "130.0.6723.58", "130.0.6723.69", "130.0.6723.70", "130.0.6723.91",
        "131.0.6778.69", "131.0.6778.85", "131.0.6778.86", "131.0.6778.108",
        "132.0.6834.83", "132.0.6834.110", "132.0.6834.159",
        "133.0.6835.57", "133.0.6835.106", "134.0.6847.45"
    ],
    'windows_versions': ["10.0", "11.0"],
    'languages': [
        "en-US,en;q=0.9",
        "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
        "zh-CN,zh;q=0.9,en;q=0.8",
        "en-US,en;q=0.9,es;q=0.8"
    ]
}

# 请求头配置
HEADERS_CONFIG = {
    'dpr_values': ["0.8", "0.9", "1", "1.25", "1.5"],
    'downlink_values': ["1.5", "2.65", "10", "1.4", "0.7"],
    'referer_probability': 0.7,     # 添加referer的概率
    'downlink_probability': 0.6,    # 添加downlink的概率
    'priority_probability': 0.5,    # 添加priority的概率
    'optional_headers_probability': 0.3,  # 添加可选头部的概率
}

# 反爬虫检测配置
ANTI_BOT_CONFIG = {
    'detection_patterns': {
        'perimeterx': ['PXu6b0qd2S', '"redirectUrl":"/blocked?', 'px-captcha'],
        'cloudflare': ['cf-ray', 'cloudflare', 'just a moment'],
        'verification': ['activate and hold the button', 'human verification'],
        'access_denied': ['access denied', 'forbidden', 'not authorized'],
        'redirect': ['/blocked?', 'uuid=', 'verification required']
    },
    'max_retries': 3,           # 最大重试次数
    'retry_delay_min': 15.0,    # 重试最小延迟
    'retry_delay_max': 45.0,    # 重试最大延迟
}

# 错误处理配置
ERROR_CONFIG = {
    'max_412_errors': 2,        # 最大412错误次数
    'max_403_errors': 3,        # 最大403错误次数
    'max_429_errors': 5,        # 最大429错误次数
    'error_cooldown': 60,       # 错误冷却时间（秒）
}

# Redis日志配置
REDIS_LOG_CONFIG = {
    'max_log_entries': 100,     # 最大日志条目数
    'log_keys': {
        'anti_bot': 'anti_bot_log',
        'error_412': 'error_412_log',
        'error_403': 'error_403_log',
        'error_429': 'error_429_log',
        'success': 'success_log'
    }
}

# 调试配置
DEBUG_CONFIG = {
    'log_headers': False,       # 是否记录请求头
    'log_response_length': True, # 是否记录响应长度
    'save_blocked_responses': False,  # 是否保存被阻止的响应
}

def get_config(config_name):
    """
    获取配置
    
    参数:
        config_name (str): 配置名称
    
    返回:
        dict: 配置字典
    """
    configs = {
        'delay': DELAY_CONFIG,
        'session': SESSION_CONFIG,
        'user_agent': USER_AGENT_CONFIG,
        'headers': HEADERS_CONFIG,
        'anti_bot': ANTI_BOT_CONFIG,
        'error': ERROR_CONFIG,
        'redis_log': REDIS_LOG_CONFIG,
        'debug': DEBUG_CONFIG
    }
    
    return configs.get(config_name, {})

def update_config(config_name, updates):
    """
    更新配置
    
    参数:
        config_name (str): 配置名称
        updates (dict): 更新的配置项
    """
    global DELAY_CONFIG, SESSION_CONFIG, USER_AGENT_CONFIG, HEADERS_CONFIG
    global ANTI_BOT_CONFIG, ERROR_CONFIG, REDIS_LOG_CONFIG, DEBUG_CONFIG
    
    if config_name == 'delay':
        DELAY_CONFIG.update(updates)
    elif config_name == 'session':
        SESSION_CONFIG.update(updates)
    elif config_name == 'user_agent':
        USER_AGENT_CONFIG.update(updates)
    elif config_name == 'headers':
        HEADERS_CONFIG.update(updates)
    elif config_name == 'anti_bot':
        ANTI_BOT_CONFIG.update(updates)
    elif config_name == 'error':
        ERROR_CONFIG.update(updates)
    elif config_name == 'redis_log':
        REDIS_LOG_CONFIG.update(updates)
    elif config_name == 'debug':
        DEBUG_CONFIG.update(updates)

if __name__ == '__main__':
    # 显示所有配置
    print("反爬虫配置信息:")
    print("=" * 50)
    
    configs = [
        ('延迟配置', DELAY_CONFIG),
        ('会话配置', SESSION_CONFIG),
        ('User-Agent配置', USER_AGENT_CONFIG),
        ('请求头配置', HEADERS_CONFIG),
        ('反爬虫检测配置', ANTI_BOT_CONFIG),
        ('错误处理配置', ERROR_CONFIG),
        ('Redis日志配置', REDIS_LOG_CONFIG),
        ('调试配置', DEBUG_CONFIG)
    ]
    
    for name, config in configs:
        print(f"\n{name}:")
        for key, value in config.items():
            print(f"  {key}: {value}")

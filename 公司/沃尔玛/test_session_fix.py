#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会话修复
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入主程序的函数
from 沃尔玛 import get_session, generate_simple_headers, generate_random_headers

def test_session_creation():
    """测试会话创建"""
    print("=" * 50)
    print("测试会话创建功能")
    print("=" * 50)
    
    try:
        # 测试会话创建
        session1 = get_session()
        print(f"✓ 第一次获取会话成功: {type(session1)}")
        
        session2 = get_session()
        print(f"✓ 第二次获取会话成功: {type(session2)}")
        
        # 检查是否是同一个会话
        if session1 is session2:
            print("✓ 会话复用正常")
        else:
            print("❌ 会话复用异常")
            
    except Exception as e:
        print(f"❌ 会话创建测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_headers_generation():
    """测试请求头生成"""
    print("\n" + "=" * 50)
    print("测试请求头生成功能")
    print("=" * 50)
    
    test_url = "https://www.walmart.com/ip/test/123456"
    
    try:
        # 测试简洁请求头
        simple_headers = generate_simple_headers(test_url)
        print(f"✓ 简洁请求头生成成功，包含{len(simple_headers)}个头部")
        
        # 测试完整请求头
        full_headers = generate_random_headers(test_url)
        print(f"✓ 完整请求头生成成功，包含{len(full_headers)}个头部")
        
    except Exception as e:
        print(f"❌ 请求头生成测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_global_variables():
    """测试全局变量"""
    print("\n" + "=" * 50)
    print("测试全局变量")
    print("=" * 50)
    
    try:
        # 导入全局变量
        from 沃尔玛 import session, session_request_count, max_session_requests
        
        print(f"✓ session变量: {session}")
        print(f"✓ session_request_count变量: {session_request_count}")
        print(f"✓ max_session_requests变量: {max_session_requests}")
        
    except Exception as e:
        print(f"❌ 全局变量测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("会话修复测试工具")
    print(f"测试时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        test_global_variables()
        test_headers_generation()
        test_session_creation()
        
        print("\n" + "=" * 50)
        print("✓ 所有测试完成")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

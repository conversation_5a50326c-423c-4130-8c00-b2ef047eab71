#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时任务测试脚本
用于测试定时任务功能是否正常工作
"""

import schedule
import time
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)

def test_job():
    """
    测试任务函数
    """
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    logging.info(f"测试任务执行 - 当前时间: {current_time}")
    print(f"✓ 测试任务执行成功 - {current_time}")

def test_scheduler():
    """
    测试定时任务调度器
    """
    print("=" * 60)
    print("定时任务测试程序")
    print("=" * 60)
    print("这个程序将每分钟执行一次测试任务，用于验证定时任务功能")
    print("按 Ctrl+C 停止测试")
    print("=" * 60)
    
    # 设置每分钟执行一次的测试任务
    schedule.every().minute.do(test_job)
    
    # 显示下次执行时间
    next_run = schedule.next_run()
    if next_run:
        print(f"下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("测试开始...")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        logging.error(f"测试异常: {e}")
    
    print("测试结束")

def test_daily_schedule():
    """
    测试每日定时任务（设置为1分钟后执行）
    """
    print("=" * 60)
    print("每日定时任务测试程序")
    print("=" * 60)
    
    # 计算1分钟后的时间
    future_time = datetime.now() + timedelta(minutes=1)
    test_time = future_time.strftime("%H:%M")
    
    print(f"设置测试任务在 {test_time} 执行（约1分钟后）")
    print("按 Ctrl+C 停止测试")
    print("=" * 60)
    
    # 设置在指定时间执行的任务
    schedule.every().day.at(test_time).do(test_job)
    
    # 显示下次执行时间
    next_run = schedule.next_run()
    if next_run:
        print(f"下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("等待任务执行...")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(10)  # 每10秒检查一次
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        logging.error(f"测试异常: {e}")
    
    print("测试结束")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--minute':
            test_scheduler()
        elif sys.argv[1] == '--daily':
            test_daily_schedule()
        elif sys.argv[1] == '--help':
            print("定时任务测试程序")
            print("使用方法:")
            print("  python test_scheduler.py --minute  # 每分钟执行一次测试")
            print("  python test_scheduler.py --daily   # 测试每日定时任务（1分钟后执行）")
            print("  python test_scheduler.py --help    # 显示帮助信息")
        else:
            print(f"未知参数: {sys.argv[1]}")
            print("使用 --help 查看帮助信息")
    else:
        print("定时任务测试程序")
        print("使用 --help 查看使用方法")

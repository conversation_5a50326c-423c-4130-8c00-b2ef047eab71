#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沃尔玛数据爬取程序安装脚本
"""

import subprocess
import sys
import os

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        print(f"当前版本: Python {version.major}.{version.minor}.{version.micro}")
        return False
    else:
        print(f"✓ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
        return True

def install_requirements():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def test_imports():
    """测试导入关键模块"""
    print("测试关键模块导入...")
    modules = [
        "requests", "pymysql", "redis", "lxml", 
        "retrying", "jmespath", "schedule"
    ]
    
    failed_modules = []
    for module in modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            print(f"❌ {module}")
            failed_modules.append(module)
    
    if failed_modules:
        print(f"❌ 以下模块导入失败: {', '.join(failed_modules)}")
        return False
    else:
        print("✓ 所有关键模块导入成功")
        return True

def main():
    """主安装函数"""
    print("=" * 60)
    print("沃尔玛数据爬取程序安装向导")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查requirements.txt文件是否存在
    if not os.path.exists("requirements.txt"):
        print("❌ 错误: 未找到requirements.txt文件")
        return False
    
    # 安装依赖包
    if not install_requirements():
        return False
    
    # 测试模块导入
    if not test_imports():
        return False
    
    print("\n" + "=" * 60)
    print("✓ 安装完成！")
    print("=" * 60)
    print("使用方法:")
    print("  立即执行一次:     python 沃尔玛.py --once")
    print("  启动定时任务:     python 沃尔玛.py --schedule")
    print("  使用启动脚本:     python start_scheduler.py")
    print("  测试定时任务:     python test_scheduler.py --help")
    print("=" * 60)
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"安装过程中发生异常: {e}")
        sys.exit(1)

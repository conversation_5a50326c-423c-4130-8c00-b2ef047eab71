#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器 - 快速切换请求头模式和其他配置
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from anti_bot_config import get_config, update_config

def show_current_config():
    """显示当前配置"""
    print("=" * 60)
    print("当前配置状态")
    print("=" * 60)
    
    # 请求头模式配置
    headers_mode = get_config('headers_mode')
    print("请求头模式配置:")
    print(f"  优先使用简洁请求头: {headers_mode['use_simple_headers']}")
    print(f"  简洁请求头概率: {headers_mode['simple_headers_probability']}")
    print(f"  强制简洁模式: {headers_mode['force_simple_mode']}")
    
    # 延迟配置
    delay_config = get_config('delay')
    print(f"\n延迟配置:")
    print(f"  最小延迟: {delay_config['min_delay']}秒")
    print(f"  最大延迟: {delay_config['max_delay']}秒")
    
    # 会话配置
    session_config = get_config('session')
    print(f"\n会话配置:")
    print(f"  每会话最大请求数: {session_config['max_requests_per_session']}")
    
    # 反爬虫配置
    anti_bot_config = get_config('anti_bot')
    print(f"\n反爬虫配置:")
    print(f"  最大重试次数: {anti_bot_config['max_retries']}")

def set_simple_mode():
    """设置为简洁模式"""
    print("设置为简洁模式...")
    update_config('headers_mode', {
        'use_simple_headers': True,
        'simple_headers_probability': 1.0,
        'force_simple_mode': True
    })
    print("✓ 已设置为简洁模式（100%使用简洁请求头）")

def set_mixed_mode():
    """设置为混合模式"""
    print("设置为混合模式...")
    update_config('headers_mode', {
        'use_simple_headers': True,
        'simple_headers_probability': 0.7,
        'force_simple_mode': False
    })
    print("✓ 已设置为混合模式（70%概率使用简洁请求头）")

def set_full_mode():
    """设置为完整模式"""
    print("设置为完整模式...")
    update_config('headers_mode', {
        'use_simple_headers': False,
        'simple_headers_probability': 0.0,
        'force_simple_mode': False
    })
    print("✓ 已设置为完整模式（100%使用完整请求头）")

def set_conservative_mode():
    """设置为保守模式（更长延迟）"""
    print("设置为保守模式...")
    update_config('delay', {
        'min_delay': 5.0,
        'max_delay': 15.0,
        'request_delay_min': 3.0,
        'request_delay_max': 8.0
    })
    print("✓ 已设置为保守模式（延迟5-15秒）")

def set_normal_mode():
    """设置为正常模式"""
    print("设置为正常模式...")
    update_config('delay', {
        'min_delay': 2.0,
        'max_delay': 8.0,
        'request_delay_min': 1.0,
        'request_delay_max': 4.0
    })
    print("✓ 已设置为正常模式（延迟2-8秒）")

def set_fast_mode():
    """设置为快速模式（较短延迟）"""
    print("设置为快速模式...")
    update_config('delay', {
        'min_delay': 1.0,
        'max_delay': 3.0,
        'request_delay_min': 0.5,
        'request_delay_max': 2.0
    })
    print("✓ 已设置为快速模式（延迟1-3秒）")

def show_menu():
    """显示菜单"""
    print("\n" + "=" * 60)
    print("配置管理器菜单")
    print("=" * 60)
    print("请求头模式:")
    print("  1. 简洁模式 - 只使用简洁请求头")
    print("  2. 混合模式 - 70%概率使用简洁请求头")
    print("  3. 完整模式 - 只使用完整请求头")
    print("\n延迟模式:")
    print("  4. 保守模式 - 延迟5-15秒（推荐用于生产环境）")
    print("  5. 正常模式 - 延迟2-8秒（默认模式）")
    print("  6. 快速模式 - 延迟1-3秒（仅用于测试）")
    print("\n其他:")
    print("  0. 显示当前配置")
    print("  q. 退出")
    print("=" * 60)

def main():
    """主函数"""
    print("沃尔玛爬虫配置管理器")
    
    while True:
        show_current_config()
        show_menu()
        
        choice = input("\n请选择操作 (0-6, q): ").strip().lower()
        
        if choice == '0':
            continue
        elif choice == '1':
            set_simple_mode()
        elif choice == '2':
            set_mixed_mode()
        elif choice == '3':
            set_full_mode()
        elif choice == '4':
            set_conservative_mode()
        elif choice == '5':
            set_normal_mode()
        elif choice == '6':
            set_fast_mode()
        elif choice == 'q':
            print("退出配置管理器")
            break
        else:
            print("无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n配置管理器异常: {e}")
        import traceback
        traceback.print_exc()

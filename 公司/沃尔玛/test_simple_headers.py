#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简洁请求头生成功能
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入主程序的函数
from 沃尔玛 import generate_simple_headers, generate_random_headers

def test_simple_headers():
    """测试简洁请求头生成"""
    print("=" * 60)
    print("测试简洁请求头生成功能")
    print("=" * 60)
    
    test_url = "https://www.walmart.com/ip/test/123456"
    
    print("生成5组简洁请求头:")
    for i in range(5):
        headers = generate_simple_headers(test_url)
        print(f"\n第{i+1}组简洁请求头:")
        print(f"User-Agent: {headers.get('user-agent')}")
        print(f"Accept-Language: {headers.get('accept-language')}")
        print(f"包含Referer: {'referer' in headers}")
        print(f"总头部数量: {len(headers)}")
        print("完整头部:")
        for key, value in headers.items():
            print(f"  '{key}': '{value}',")

def test_headers_comparison():
    """对比简洁头部和完整头部"""
    print("\n" + "=" * 60)
    print("对比简洁头部和完整头部")
    print("=" * 60)
    
    test_url = "https://www.walmart.com/ip/test/123456"
    
    # 生成简洁头部
    simple_headers = generate_simple_headers(test_url)
    print("简洁头部:")
    print(f"  头部数量: {len(simple_headers)}")
    print(f"  User-Agent: {simple_headers.get('user-agent')}")
    print(f"  Accept-Language: {simple_headers.get('accept-language')}")
    
    # 生成完整头部
    full_headers = generate_random_headers(test_url)
    print("\n完整头部:")
    print(f"  头部数量: {len(full_headers)}")
    print(f"  User-Agent: {full_headers.get('user-agent')}")
    print(f"  Accept-Language: {full_headers.get('accept-language')}")
    print(f"  包含DPR: {'dpr' in full_headers}")
    print(f"  包含Sec-CH-UA: {'sec-ch-ua' in full_headers}")

def generate_sample_headers():
    """生成示例请求头代码"""
    print("\n" + "=" * 60)
    print("生成示例请求头代码")
    print("=" * 60)
    
    test_url = "https://www.walmart.com/ip/test/123456"
    
    # 生成3组简洁请求头
    for i in range(3):
        headers = generate_simple_headers(test_url)
        print(f"\n# 示例 {i+1} - 简洁请求头")
        print("headers = {")
        for key, value in headers.items():
            print(f"    '{key}': '{value}',")
        print("}")

def test_user_agent_diversity():
    """测试User-Agent多样性"""
    print("\n" + "=" * 60)
    print("测试User-Agent多样性")
    print("=" * 60)
    
    test_url = "https://www.walmart.com/ip/test/123456"
    
    # 简洁头部User-Agent多样性
    simple_uas = set()
    for i in range(50):
        headers = generate_simple_headers(test_url)
        simple_uas.add(headers.get('user-agent'))
    
    # 完整头部User-Agent多样性
    full_uas = set()
    for i in range(50):
        headers = generate_random_headers(test_url)
        full_uas.add(headers.get('user-agent'))
    
    print(f"简洁头部 - 50次生成的不同User-Agent数量: {len(simple_uas)}")
    print(f"完整头部 - 50次生成的不同User-Agent数量: {len(full_uas)}")
    
    print(f"\n简洁头部User-Agent样例:")
    for i, ua in enumerate(list(simple_uas)[:3]):
        print(f"  {i+1}. {ua}")

def save_sample_headers():
    """保存示例请求头到文件"""
    print("\n" + "=" * 60)
    print("保存示例请求头")
    print("=" * 60)
    
    test_url = "https://www.walmart.com/ip/test/123456"
    
    samples = {
        'timestamp': datetime.now().isoformat(),
        'simple_headers': [],
        'full_headers': []
    }
    
    # 生成5组简洁请求头
    for i in range(5):
        headers = generate_simple_headers(test_url)
        samples['simple_headers'].append({
            'index': i + 1,
            'headers': dict(headers)
        })
    
    # 生成5组完整请求头
    for i in range(5):
        headers = generate_random_headers(test_url)
        samples['full_headers'].append({
            'index': i + 1,
            'headers': dict(headers)
        })
    
    # 保存到文件
    filename = f"headers_samples_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(samples, f, indent=2, ensure_ascii=False)
    
    print(f"示例请求头已保存到: {filename}")

def main():
    """主测试函数"""
    print("简洁请求头测试工具")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行所有测试
        test_simple_headers()
        test_headers_comparison()
        generate_sample_headers()
        test_user_agent_diversity()
        save_sample_headers()
        
        print("\n" + "=" * 60)
        print("✓ 所有测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

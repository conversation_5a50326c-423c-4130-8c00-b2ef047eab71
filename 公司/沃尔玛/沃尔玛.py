import logging
import random
import time
from datetime import datetime
import jmespath
import pymysql
import redis
import requests
import json
import re
from retrying import retry
from lxml import html
import schedule
import signal
import sys
from anti_bot_config import get_config

# 配置日志格式，包括时间信息
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',  # 包含时间、日志级别和日志消息
    datefmt='%Y-%m-%d %H:%M:%S',  # 时间格式
)

# Redis 连接配置
# 设置Redis服务器连接信息
host = '*************'
port = 6379
db = 2
password = 'TezgyWhIxYHkBfci'

# 创建Redis客户端实例并设置账号和密码
redis_client = redis.StrictRedis(
    host=host,
    port=port,
    db=db,
    password=password
)

# 初始化重试计数器和user-agent
retry_count = 0
current_user_agent = None
ua_fail_count = 0  # User-Agent失败计数器
error_412_count = 0  # 412错误计数器
max_412_errors = 2  # 最大412错误次数

# 全局会话对象
session = None
session_request_count = 0  # 会话请求计数
max_session_requests = 50  # 每个会话最大请求数


def normalize_walmart_url(url):
    """
    标准化沃尔玛URL格式

    参数:
        url (str): 原始URL

    返回:
        str: 标准化后的URL
    """
    if not url or not isinstance(url, str):
        raise ValueError("URL不能为空且必须是字符串")

    # 去除首尾空格
    url = url.strip()

    # 检查URL是否为空
    if not url:
        raise ValueError("URL不能为空")

    # 如果没有协议头，添加https://
    if not url.startswith(('http://', 'https://')):
        if url.startswith('www.walmart.com') or url.startswith('walmart.com'):
            url = 'https://' + url.lstrip('www.')
        elif url.startswith('//'):
            # 处理 //walmart.com 格式
            url = 'https:' + url
        else:
            # 如果看起来像沃尔玛URL路径，添加完整域名
            if url.startswith('/ip/') or '/ip/' in url:
                if not url.startswith('/'):
                    url = '/' + url
                url = 'https://www.walmart.com' + url
            else:
                # 其他情况，假设是完整域名但缺少协议
                url = 'https://' + url

    # 确保是沃尔玛域名
    if 'walmart.com' not in url:
        logging.warning(f"URL不是沃尔玛域名: {url}")

    # 验证URL格式
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        if not parsed.netloc:
            raise ValueError(f"无效的URL格式: {url}")
    except Exception as e:
        logging.error(f"URL解析失败: {e}")
        raise ValueError(f"URL格式错误: {url}")

    return url


def safe_get_nested_value(data, path, default=None):
    """
    安全地从嵌套字典中获取值，支持数组索引访问

    参数:
        data: 要搜索的字典
        path: 路径字符串，如 'props.pageProps.initialData.data.product.fulfillmentOptions[0].availableQuantity'
        default: 如果路径不存在时返回的默认值

    返回:
        提取的值或默认值
    """
    try:
        # 将路径拆分为键和索引
        import re
        # 分割路径，处理数组访问
        parts = re.split(r'\.|\[|\]', path)
        parts = [part for part in parts if part]  # 移除空字符串

        current = data
        i = 0
        while i < len(parts):
            part = parts[i]

            # 检查是否是数组索引
            if part.isdigit():
                index = int(part)
                if isinstance(current, list) and 0 <= index < len(current):
                    current = current[index]
                else:
                    return default
            else:
                # 普通字典访问
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return default
            i += 1

        return current
    except (TypeError, KeyError, IndexError, AttributeError, ValueError):
        return default


def get_user_agent():
    """
    获取User-Agent，只有在连续失败3次以上时才更换
    增加预定义的高质量User-Agent列表作为备选

    返回:
        str: User-Agent字符串
    """
    global current_user_agent, ua_fail_count

    # 预定义的高质量User-Agent列表（从ua.tet文件中精选的桌面浏览器）
    predefined_user_agents = [
        # 最新Chrome浏览器
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.62 Safari/537.36",
        "Mozilla/5.0 (Linux; Android 11; PCGM00 Build/RKQ1.201217.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36 os/android model/PCGM00 build/7740200 osVer/11 sdkInt/30 network/2 BiliApp/7740200 mobi_app/android channel/oppo Buvid/XUC8661CE00F7965486DB0DBBEF690A732D40 sessionID/5b12ba4d innerVer/7740210 c_locale/zh_CN s_locale/zh_CN disable_rcmd/0",
        "Mozilla/5.0 (Linux; Android 10; GLK-AL00 Build/HUAWEIGLK-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/99.0.4844.88 Mobile Safari/537.36 os/android model/GLK-AL00 build/8200200 osVer/10 sdkInt/29 network/1 BiliApp/8200200 mobi_app/android channel/huawei Buvid/XZ4586A449D133EB25AC412AB208FF5003E46 sessionID/81245ee4 innerVer/8200210 c_locale/zh-Hans_CN s_locale/zh-Hans_CN disable_rcmd/0 themeId/1 sh/34",
        "Dalvik/2.1.0 (Linux; U; Android 11; SM-A8050 Build/RP1A.200720.012)",
        "Mozilla/5.0 (Linux; Android 14; V2244A Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/130.0.6723.73 Mobile Safari/537.36 XWEB/1300057 MMWEBSDK/20240802 MMWEBID/1409 MicroMessenger/8.0.53.2740(0x28003533) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.70 Safari/537.36"
    ]

    # 如果是第一次使用或者失败次数超过3次，则生成新的User-Agent
    if current_user_agent is None or ua_fail_count > 3:
        try:
            # 70%的概率使用预定义的高质量User-Agent，30%的概率使用随机生成的
            if random.random() < 0.7:
                current_user_agent = random.choice(predefined_user_agents)
                logging.info(f"使用预定义User-Agent: {current_user_agent}")
            else:
                ua = UserAgent()
                current_user_agent = ua.random
                logging.info(f"使用随机生成User-Agent: {current_user_agent}")
        except Exception as e:
            # 如果UserAgent库出错，回退到预定义列表
            logging.warning(f"UserAgent库出错，使用预定义User-Agent: {e}")
            current_user_agent = random.choice(predefined_user_agents)

        ua_fail_count = 0  # 重置失败计数器
        logging.info(f"更换User-Agent: {current_user_agent}")

    return current_user_agent


def reset_ua_fail_count():
    """
    重置User-Agent失败计数器（请求成功时调用）
    """
    global ua_fail_count
    ua_fail_count = 0


def increment_ua_fail_count():
    """
    增加User-Agent失败计数器（请求失败时调用）
    """
    global ua_fail_count
    ua_fail_count += 1
    logging.info(f"User-Agent失败计数: {ua_fail_count}")


def get_database_connection():
    """
    获取数据库连接

    返回:
        pymysql.Connection: 数据库连接对象
    """
    return pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,
        charset='utf8mb4'
    )


def extract_walmart_item_id(url):
    """
    从沃尔玛URL中提取商品ID

    参数:
        url (str): 沃尔玛商品URL

    返回:
        str or None: 提取的商品ID，如果提取失败返回None
    """
    url_match = re.search(r'/ip/.*?/(\d+)', url)
    return url_match.group(1) if url_match else None


def delete_item_records(connection, walmart_item_id):
    """
    从数据库中删除指定商品ID的记录

    参数:
        connection: 数据库连接对象
        walmart_item_id (str): 要删除的商品ID

    返回:
        tuple: (删除的sgm_walmart_item行数, 删除的sgm_walmart_item_subscribe行数)
    """
    try:
        with connection.cursor() as cursor:
            # 删除sgm_walmart_item表中的记录
            delete_sql_item = "DELETE FROM sgm_walmart_item WHERE walmart_item_id = %s"
            deleted_rows_item = cursor.execute(delete_sql_item, (walmart_item_id,))

            # 删除sgm_walmart_item_subscribe表中的记录
            delete_sql_subscribe = "DELETE FROM sgm_walmart_item_subscribe WHERE walmart_item_id = %s"
            deleted_rows_subscribe = cursor.execute(delete_sql_subscribe, (walmart_item_id,))

            connection.commit()
            return deleted_rows_item, deleted_rows_subscribe

    except pymysql.Error as db_error:
        logging.error(f"删除数据库记录失败 - walmart_item_id: {walmart_item_id}, 错误: {db_error}")
        raise
    except Exception as e:
        logging.error(f"删除操作异常 - walmart_item_id: {walmart_item_id}, 错误: {e}")
        raise


def handle_404_error(url, connection):
    """
    处理404错误，从URL中提取商品ID并删除数据库中的相关记录

    参数:
        url (str): 404错误的URL
        connection: 数据库连接对象
    """
    logging.info(f"链接信息不存在:{url}")

    # 从URL中提取walmart_item_id
    walmart_item_id = extract_walmart_item_id(url)

    if walmart_item_id:
        logging.info(f"从URL提取到的商品ID: {walmart_item_id}")

        try:
            deleted_rows_item, deleted_rows_subscribe = delete_item_records(connection, walmart_item_id)
            logging.info(
                f"已删除数据库记录 - walmart_item_id: {walmart_item_id}, sgm_walmart_item表删除{deleted_rows_item}行, sgm_walmart_item_subscribe表删除{deleted_rows_subscribe}行")
        except Exception as e:
            logging.error(f"删除记录时发生错误: {e}")
    else:
        logging.warning(f"无法从URL中提取商品ID: {url}")


def handle_412_error(url):
    """
    处理412 Precondition Failed错误，记录详细信息并准备重试策略

    参数:
        url (str): 发生412错误的URL
    """
    logging.warning(f"遇到412 Precondition Failed错误")
    logging.info(f"URL: {url}")

    # 记录到Redis，用于分析412错误模式
    try:
        error_info = {
            'url': url,
            'timestamp': time.time(),
            'error_type': '412_precondition_failed'
        }
        redis_client.lpush('error_412_log', json.dumps(error_info))
        redis_client.ltrim('error_412_log', 0, 99)  # 只保留最近100条记录
        logging.info("412错误信息已记录到Redis")
    except Exception as e:
        logging.error(f"记录412错误信息到Redis失败: {e}")

    # 增加延迟，避免过于频繁的请求
    delay = random.uniform(5, 15)
    logging.info(f"412错误处理：等待{delay:.2f}秒后重试")
    time.sleep(delay)


def insert_item_data(connection, item_data):
    """
    将商品数据插入数据库

    参数:
        connection: 数据库连接对象
        item_data (dict): 包含商品信息的字典
    """
    try:
        with connection.cursor() as cursor:
            sql = """
                  INSERT INTO sgm_walmart_item (available_quantity, walmart_item_id, price, rating, total_media_count, \
                                                total_review_count, reviews_with_text_count, percentage_five_count, \
                                                percentage_four_count, percentage_three_count, percentage_two_count, \
                                                percentage_one_count, create_datetime, update_datetime, shipPrice)
                  VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) \
                  """
            cursor.execute(sql, (
                item_data['availableQuantity'], item_data['itemId'], item_data['price'],
                item_data['rating'], item_data['total_media_count'], item_data['total_review_count'],
                item_data['reviews_with_text_count'], item_data['percentage_five_count'],
                item_data['percentage_four_count'], item_data['percentage_three_count'],
                item_data['percentage_two_count'], item_data['percentage_one_count'],
                item_data['current_time'], item_data['current_time'], item_data['shipPrice']
            ))

            logging.info(f"数据成功插入数据库 - ItemId: {item_data['itemId']}")

    except pymysql.Error as db_error:
        logging.error(f"数据库插入失败 - ItemId: {item_data['itemId']}, 错误: {db_error}")
        raise
    except Exception as e:
        logging.error(f"数据库操作异常 - ItemId: {item_data['itemId']}, 错误: {e}")
        raise


def safe_get_array_item(data, index, default=None):
    """
    安全地从数组中获取指定索引的元素

    参数:
        data: 要搜索的数组
        index: 数组索引
        default: 如果索引不存在时返回的默认值

    返回:
        提取的值或默认值
    """
    try:
        if isinstance(data, list) and 0 <= index < len(data):
            return data[index]
        return default
    except (TypeError, IndexError):
        return default


def generate_simple_headers(url):
    """
    生成简洁的随机请求头，模拟真实浏览器行为

    参数:
        url (str): 目标URL

    返回:
        dict: 简洁的随机请求头
    """
    # Chrome版本池（最新版本）
    chrome_versions = [
        "130.0.6723.58", "130.0.6723.69", "130.0.6723.70", "130.0.6723.91",
        "131.0.6778.69", "131.0.6778.85", "131.0.6778.86", "131.0.6778.108",
        "132.0.6834.83", "132.0.6834.110", "132.0.6834.159",
        "133.0.6835.57", "133.0.6835.106", "134.0.6847.45",
        "135.0.6858.23", "136.0.6869.15", "137.0.6880.45",
        "138.0.6891.23", "139.0.6902.15"
    ]

    # 语言设置
    languages = [
        "zh-CN,zh;q=0.9",
        "en-US,en;q=0.9",
        "zh-CN,zh;q=0.9,en;q=0.8",
        "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7"
    ]

    # Windows版本
    windows_versions = ["10.0", "11.0"]

    # 随机选择
    chrome_version = random.choice(chrome_versions)
    language = random.choice(languages)
    windows_version = random.choice(windows_versions)

    # 生成User-Agent
    user_agent = f"Mozilla/5.0 (Windows NT {windows_version}; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"

    # 基础请求头（简洁版）
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': language,
        'cache-control': 'no-cache',
        'user-agent': user_agent,
    }

    # 80%概率添加referer
    if random.random() < 0.8:
        headers['referer'] = 'https://www.walmart.com/'

    return headers


def generate_random_headers(url):
    """
    生成随机的请求头，模拟真实浏览器行为

    参数:
        url (str): 目标URL

    返回:
        dict: 随机生成的请求头
    """
    # 获取配置
    ua_config = get_config('user_agent')
    headers_config = get_config('headers')

    # 随机选择版本
    chrome_version = random.choice(ua_config['chrome_versions'])
    windows_version = random.choice(ua_config['windows_versions'])

    # 生成User-Agent
    user_agent = f"Mozilla/5.0 (Windows NT {windows_version}; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"

    # 生成sec-ch-ua
    chrome_major = chrome_version.split('.')[0]
    sec_ch_ua = f'"Google Chrome";v="{chrome_major}", "Chromium";v="{chrome_major}", "Not?A_Brand";v="99"'

    # 基础请求头
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': random.choice(ua_config['languages']),
        'cache-control': 'no-cache',
        'dpr': random.choice(headers_config['dpr_values']),
        'pragma': 'no-cache',
        'sec-ch-ua': sec_ch_ua,
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': user_agent,
    }

    # 随机添加一些可选头部
    if random.random() < headers_config['referer_probability']:
        headers['referer'] = 'https://www.walmart.com/'

    if random.random() < headers_config['downlink_probability']:
        headers['downlink'] = random.choice(headers_config['downlink_values'])

    if random.random() < headers_config['priority_probability']:
        headers['priority'] = 'u=0, i'

    # 随机添加一些真实浏览器会发送的头部
    optional_headers = {
        'sec-ch-ua-arch': '"x86"',
        'sec-ch-ua-bitness': '"64"',
        'sec-ch-ua-full-version-list': f'"Google Chrome";v="{chrome_version}", "Chromium";v="{chrome_version}", "Not?A_Brand";v="99.0.0.0"',
        'sec-ch-ua-wow64': '?0',
        'viewport-width': str(random.randint(1200, 1920)),
    }

    # 随机添加一些可选头部
    for header, value in optional_headers.items():
        if random.random() < headers_config['optional_headers_probability']:
            headers[header] = value

    return headers


def generate_edge_ua():
    """生成简洁的 Microsoft Edge User-Agent 字符串"""
    # Windows 版本号范围
    windows_versions = [
        "10.0",  # Windows 10
        "11.0"  # Windows 11
    ]

    # Chrome 和 Edge 版本号范围 (基于实际版本范围)
    chrome_major = random.randint(120, 140)  # 主版本号 (120-140 是常见范围)
    chrome_minor = random.randint(0, 99)  # 次要版本号
    chrome_build = random.randint(1000, 9999)  # 构建号

    # 构建版本字符串 - 使用更常见的格式
    chrome_version = f"{chrome_major}.{chrome_minor}.{chrome_build}"
    edge_version = f"{chrome_major}.{chrome_minor}.{chrome_build}"

    # 组装简洁的 User-Agent 字符串
    ua = (
        f"Mozilla/5.0 (Windows NT {random.choice(windows_versions)}; Win64; x64) "
        f"AppleWebKit/537.36 (KHTML, like Gecko) "
        f"Chrome/{chrome_version} Safari/537.36 "
        f"Edg/{edge_version}"
    )

    return ua


def detect_anti_bot_response(response_text, url):
    """
    检测反爬虫响应

    参数:
        response_text (str): 响应文本
        url (str): 请求的URL

    返回:
        tuple: (is_blocked, block_type, details)
    """
    # 获取反爬虫检测配置
    anti_bot_config = get_config('anti_bot')
    patterns = anti_bot_config['detection_patterns']

    response_lower = response_text.lower()

    # 检测PerimeterX反爬虫
    for pattern in patterns['perimeterx']:
        if pattern.lower() in response_lower:
            return True, 'PerimeterX', 'PerimeterX反爬虫检测'

    # 检测Cloudflare
    for pattern in patterns['cloudflare']:
        if pattern.lower() in response_lower:
            return True, 'Cloudflare', 'Cloudflare防护'

    # 检测验证页面
    for pattern in patterns['verification']:
        if pattern.lower() in response_lower:
            return True, 'Verification', '人机验证页面'

    # 检测访问被拒绝
    for pattern in patterns['access_denied']:
        if pattern.lower() in response_lower:
            return True, 'AccessDenied', '访问被拒绝'

    # 检测重定向到验证页面
    for pattern in patterns['redirect']:
        if pattern in response_text:  # 这些模式区分大小写
            return True, 'Redirect', '重定向到验证页面'

    return False, None, None


def handle_anti_bot_detection(url, block_type, details):
    """
    处理反爬虫检测

    参数:
        url (str): 被检测的URL
        block_type (str): 检测类型
        details (str): 详细信息
    """
    global current_user_agent, ua_fail_count

    logging.warning(f"检测到反爬虫防护 - 类型: {block_type}, 详情: {details}")
    logging.warning(f"URL: {url}")

    # 强制更换User-Agent
    current_user_agent = None
    ua_fail_count = 999

    # 记录到Redis用于分析
    try:
        error_info = {
            'url': url,
            'timestamp': time.time(),
            'block_type': block_type,
            'details': details,
            'user_agent': current_user_agent
        }
        redis_client.lpush('anti_bot_log', json.dumps(error_info))
        redis_client.ltrim('anti_bot_log', 0, 99)  # 只保留最近100条记录
        logging.info("反爬虫检测信息已记录到Redis")
    except Exception as e:
        logging.error(f"记录反爬虫信息到Redis失败: {e}")

    # 获取延迟配置
    anti_bot_config = get_config('anti_bot')
    delay = random.uniform(anti_bot_config['retry_delay_min'], anti_bot_config['retry_delay_max'])
    logging.info(f"反爬虫检测处理：等待{delay:.2f}秒后重试")
    time.sleep(delay)


def get_session():
    """
    获取或创建会话对象

    返回:
        requests.Session: 会话对象
    """
    global session, session_request_count

    # 获取会话配置
    session_config = get_config('session')

    # 如果会话不存在或请求次数超过限制，创建新会话
    if session is None or session_request_count >= session_config['max_requests_per_session']:
        if session:
            session.close()
            logging.info(f"关闭旧会话，已处理{session_request_count}个请求")

        session = requests.Session()
        session_request_count = 0

        # 设置会话级别的配置
        session.headers.update({
            'Connection': 'keep-alive',
            'Accept-Encoding': 'gzip, deflate, br',
        })

        logging.info("创建新的请求会话")

    session_request_count += 1
    return session


def simulate_human_behavior():
    """
    模拟人类浏览行为
    """
    # 获取延迟配置
    delay_config = get_config('delay')

    # 随机延迟
    delay = random.uniform(delay_config['min_delay'], delay_config['max_delay'])
    logging.debug(f"模拟人类行为延迟: {delay:.2f}秒")
    time.sleep(delay)

    # 模拟鼠标移动（通过添加随机的viewport相关头部）
    if random.random() < 0.3:  # 30%概率
        time.sleep(random.uniform(0.1, 0.5))


@retry(stop_max_attempt_number=5, wait_fixed=6000)
def getData(url):
    """
    获取指定URL的页面数据并解析所需信息。

    参数:
        url (str): 要请求的URL。
        retries (int): 重试次数。
        delay (int): 每次重试之间的延迟时间（秒）。

    返回:
        None
    """
    global retry_count, current_user_agent, ua_fail_count, error_412_count, max_412_errors

    # 使用专门的URL处理函数标准化URL
    try:
        original_url = url
        url = normalize_walmart_url(url)
        if url != original_url:
            logging.info(f"URL已标准化: {original_url} -> {url}")
    except ValueError as e:
        logging.error(f"URL格式错误: {e}")
        return  # 跳过无效URL

    # 获取数据库连接
    connection = get_database_connection()

    try:
        # 模拟人类浏览行为
        simulate_human_behavior()

        # 根据配置选择请求头模式
        headers_mode_config = get_config('headers_mode')

        if headers_mode_config['force_simple_mode']:
            # 强制简洁模式
            headers = generate_simple_headers(url)
            logging.debug(f"使用简洁请求头（强制模式） - User-Agent: {headers.get('user-agent', 'Unknown')}")
        elif headers_mode_config['use_simple_headers'] and random.random() < headers_mode_config['simple_headers_probability']:
            # 按概率使用简洁模式
            headers = generate_simple_headers(url)
            logging.debug(f"使用简洁请求头 - User-Agent: {headers.get('user-agent', 'Unknown')}")
        else:
            # 使用完整模式
            headers = generate_random_headers(url)
            logging.debug(f"使用完整请求头 - User-Agent: {headers.get('user-agent', 'Unknown')}")

        # 获取会话对象（在生成请求头之后）
        current_session = get_session()

        # 发送请求（使用会话）
        logging.debug(f"使用会话请求 (第{session_request_count}个请求)")
        response = current_session.get(url, headers=headers, timeout=30, allow_redirects=True)

        # 检测反爬虫响应
        is_blocked, block_type, details = detect_anti_bot_response(response.text, url)
        if is_blocked:
            handle_anti_bot_detection(url, block_type, details)
            raise Exception(f"反爬虫检测: {block_type} - {details}")
        # 针对不同状态码的处理
        if response.status_code == 404:
            handle_404_error(url, connection)
            return
        elif response.status_code == 412:
            print(response.text)
            error_412_count += 1
            logging.warning(f"遇到412错误（第{error_412_count}次），尝试更换请求策略: {url}")
            if error_412_count > max_412_errors:
                logging.warning(f"412错误超过{max_412_errors}次，跳过该URL: {url}")
                return  # 跳过该URL
            raise Exception(f"412 Precondition Failed - 需要更换请求策略")
        elif response.status_code == 403:
            # 403 Forbidden 处理
            logging.warning(f"遇到403错误，可能被反爬虫检测: {url}")
            raise Exception(f"403 Forbidden - 访问被拒绝，可能需要更换User-Agent")
        elif response.status_code == 429:
            # 429 Too Many Requests 处理
            logging.warning(f"遇到429错误，请求过于频繁: {url}")
            time.sleep(random.uniform(10, 20))  # 增加等待时间
            raise Exception(f"429 Too Many Requests - 请求过于频繁")
        elif response.status_code not in [200, 301, 302]:
            raise Exception(f"请求失败，状态码: {response.status_code}, 响应: {response.text[:200]}")

        # 检测验证页面
        tree = html.fromstring(response.text)
        verification_elements = tree.xpath('//*[contains(text(),"Activate and hold the button to confirm that")]')
        if verification_elements:
            logging.warning(f"检测到验证页面: {url}")
            raise Exception("检测到验证页面，需要人工干预")

        # 安全地查找并提取JSON数据
        script_matches = re.findall('''<script id="__NEXT_DATA__" type="application/json" .*?>(.*?)</script>''',
                                    response.text)

        if not script_matches:
            logging.warning(f"未找到__NEXT_DATA__脚本标签: {url}")
            return

        script_tag = script_matches[0]

        if script_tag:
            try:
                json_data = json.loads(script_tag)
            except json.JSONDecodeError as e:
                logging.error(f"JSON解析失败: {e}, URL: {url}")
                return
            # 安全地提取所有数据，设置默认值
            # 库存
            availableQuantity = safe_get_nested_value(json_data,
                                                      'props.pageProps.initialData.data.product.fulfillmentOptions[0].availableQuantity',
                                                      0)

            # 产品id - 这是关键字段，如果为空则跳过
            itemId = safe_get_nested_value(json_data,
                                           'props.pageProps.initialData.data.contentLayout.pageMetadata.pageContext.itemContext.itemId')
            if not itemId:
                logging.warning(f"无法获取产品ID，跳过此商品: {url}")
                return

            # 价格
            price = safe_get_nested_value(json_data,
                                          'props.pageProps.initialData.data.product.priceInfo.currentPrice.price', 0)
            shipPrice = safe_get_nested_value(json_data,
                                              "props.pageProps.initialData.data.product.fulfillmentOptions[0].speedDetails.fulfillmentPrice.price",
                                              0)

            # 标题
            title = safe_get_nested_value(json_data, 'props.pageProps.initialData.data.product.name', "")

            # 评分相关数据，设置默认值
            rating = safe_get_nested_value(json_data,
                                           'props.pageProps.initialData.data.reviews.roundedAverageOverallRating', 0)
            total_media_count = safe_get_nested_value(json_data,
                                                      'props.pageProps.initialData.data.reviews.totalMediaCount', 0)
            total_review_count = safe_get_nested_value(json_data,
                                                       'props.pageProps.initialData.data.reviews.totalReviewCount', 0)
            reviews_with_text_count = safe_get_nested_value(json_data,
                                                            'props.pageProps.initialData.data.reviews.reviewsWithTextCount',
                                                            0)
            percentage_five_count = safe_get_nested_value(json_data,
                                                          'props.pageProps.initialData.data.reviews.percentageFiveCount',
                                                          0)
            percentage_four_count = safe_get_nested_value(json_data,
                                                          'props.pageProps.initialData.data.reviews.percentageFourCount',
                                                          0)
            percentage_three_count = safe_get_nested_value(json_data,
                                                           'props.pageProps.initialData.data.reviews.percentageThreeCount',
                                                           0)
            percentage_two_count = safe_get_nested_value(json_data,
                                                         'props.pageProps.initialData.data.reviews.percentageTwoCount',
                                                         0)
            percentage_one_count = safe_get_nested_value(json_data,
                                                         'props.pageProps.initialData.data.reviews.percentageOneCount',
                                                         0)

            # 记录成功提取的数据用于调试
            logging.info(f"成功提取数据 - ItemId: {itemId}, 价格: {price}, 库存: {availableQuantity}, 评分: {rating}")

            # 链接
            walmart_item_url = url

            # 获取当前时间
            current_time = datetime.now()

            # 将当前时间格式化为字符串
            current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')

            # 将格式化的字符串解析回 datetime 对象
            current_time_obj = datetime.strptime(current_time_str, '%Y-%m-%d %H:%M:%S')

            # 准备商品数据字典
            item_data = {
                'availableQuantity': availableQuantity,
                'itemId': itemId,
                'price': price,
                'rating': rating,
                'total_media_count': total_media_count,
                'total_review_count': total_review_count,
                'reviews_with_text_count': reviews_with_text_count,
                'percentage_five_count': percentage_five_count,
                'percentage_four_count': percentage_four_count,
                'percentage_three_count': percentage_three_count,
                'percentage_two_count': percentage_two_count,
                'percentage_one_count': percentage_one_count,
                'current_time': current_time_obj,
                'shipPrice': shipPrice
            }

            # 插入数据库
            insert_item_data(connection, item_data)

            # 请求成功后重置所有失败计数器
            retry_count = 0
            reset_ua_fail_count()  # 重置User-Agent失败计数器

    except requests.exceptions.RequestException as req_e:
        retry_count = retry_count + 1
        increment_ua_fail_count()
        logging.error(f"网络请求异常: {req_e}, 重试次数: {retry_count}")

        # 根据异常类型采取不同策略
        if "412" in str(req_e) or hasattr(req_e, 'response') and req_e.response and req_e.response.status_code == 412:
            error_412_count += 1
            logging.warning(f"检测到412错误（第{error_412_count}次），强制更换User-Agent")

            if error_412_count > max_412_errors:
                logging.warning(f"412错误超过{max_412_errors}次，跳过该URL: {url}")
                return  # 跳过该URL

            current_user_agent = None
            ua_fail_count = 999  # 强制更换

        # 如果失败次数超过2次，增加延迟
        if retry_count > 2:
            logging.info("连续失败超过两次，增加延迟...")
            time.sleep(random.uniform(5, 10))
            retry_count = 0
        raise
    except Exception as e:
        retry_count = retry_count + 1
        increment_ua_fail_count()
        logging.error(f"请求失败，重新尝试: {e}, 重试次数: {retry_count}")

        # 如果失败次数超过2次，增加延迟
        if retry_count > 2:
            logging.info("连续失败超过两次...")
            retry_count = 0
        raise
    finally:
        connection.close()


def get_info():
    """
    主要的数据爬取函数
    """
    logging.info("========程序开始执行（无代理模式）=====")

    # 记录开始时间
    start_time = time.time()
    # 获取数据库连接
    connection = get_database_connection()
    failed_urls = []  # 用于存储处理失败的 URL

    try:
        with connection.cursor() as cursor:
            # 查询数据库
            sql = "SELECT * FROM sgm_walmart_item_subscribe ORDER BY create_datetime DESC"
            cursor.execute(sql)
            # 获取查询结果
            results = cursor.fetchall()
            # # 处理查询结果
            for index, row in enumerate(results):
                # if index <= 53:
                #     continue
                url = row[7]
                logging.info(f"总数: {len(results)} 已经完成数量: {index + 1} 目前爬取商品URL: {url}")

                # 重置412错误计数器（每个新URL开始时重置）
                global error_412_count
                error_412_count = 0

                # 预先验证URL格式
                try:
                    normalized_url = normalize_walmart_url(url)
                    if normalized_url != url:
                        logging.info(f"数据库URL已标准化: {url} -> {normalized_url}")
                        url = normalized_url
                except ValueError as url_error:
                    logging.error(f"数据库URL格式错误，跳过: {url}, 错误: {url_error}")
                    failed_urls.append(url)
                    continue

                try:
                    getData(url)  # 尝试调用 getData(url)
                except Exception as e:
                    logging.error(f"处理商品 {url} 时发生错误: {e}")
                    failed_urls.append(url)  # 记录失败的 URL

        # 记录失败的 URL 列表
        if failed_urls:
            logging.info(f"处理失败的URL数量: {len(failed_urls)}，列表如下:")
            for failed_url in failed_urls:
                logging.info(f"失败的URL: {failed_url}")
        else:
            logging.info("所有URL处理成功，无失败的URL")

    except Exception as e:
        logging.error(f"数据爬取过程中发生异常: {e}")
        raise
    finally:
        connection.close()

    # 记录结束时间并计算总耗时
    end_time = time.time()
    total_time = end_time - start_time  # 计算总时间，单位为秒
    # 将总时间格式化为小时、分钟、秒
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)

    logging.info(f"========程序执行结束，耗时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒 ========")


# 全局变量控制程序运行状态
running = True


def signal_handler(signum, frame):
    """
    信号处理函数，用于优雅地停止程序
    """
    global running
    logging.info("接收到停止信号，正在优雅地关闭程序...")
    running = False


def scheduled_job():
    """
    定时任务执行的函数
    """
    try:
        logging.info("=== 定时任务开始执行 ===")
        get_info()
        logging.info("=== 定时任务执行完成 ===")
    except Exception as e:
        logging.error(f"定时任务执行失败: {e}")


def run_scheduler():
    """
    运行定时任务调度器
    """
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 设置定时任务：每天早上8点执行
    schedule.every().day.at("08:00").do(scheduled_job)

    logging.info("定时任务调度器已启动")
    logging.info("任务将在每天早上8:00执行")
    logging.info("按 Ctrl+C 可以停止程序")

    # 显示下次执行时间
    next_run = schedule.next_run()
    if next_run:
        logging.info(f"下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")

    # 主循环
    while running:
        try:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logging.info("接收到键盘中断信号")
            break
        except Exception as e:
            logging.error(f"调度器运行异常: {e}")
            time.sleep(60)  # 出错后等待1分钟再继续

    logging.info("定时任务调度器已停止")


def run_once():
    """
    立即执行一次任务（用于测试）
    """
    try:
        logging.info("=== 立即执行任务 ===")
        get_info()
    except KeyboardInterrupt:
        logging.info("程序被用户中断")
    except Exception as e:
        logging.error(f"程序执行异常: {e}")
        raise


if __name__ == '__main__':
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--once':
            # 立即执行一次
            run_once()
        elif sys.argv[1] == '--schedule':
            # 运行定时任务
            run_scheduler()
        elif sys.argv[1] == '--help':
            print("使用方法:")
            print("  python 沃尔玛.py --once      # 立即执行一次")
            print("  python 沃尔玛.py --schedule  # 启动定时任务（每天8:00执行）")
            print("  python 沃尔玛.py --help      # 显示帮助信息")
            print("  python 沃尔玛.py             # 默认立即执行一次")
        else:
            print(f"未知参数: {sys.argv[1]}")
            print("使用 --help 查看帮助信息")
    else:
        # 默认行为：立即执行一次（保持向后兼容）
        run_once()

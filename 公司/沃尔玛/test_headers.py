#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试请求头生成和反爬虫检测功能
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入主程序的函数
from 沃尔玛 import generate_random_headers, detect_anti_bot_response

def test_header_generation():
    """测试请求头生成功能"""
    print("=" * 60)
    print("测试请求头生成功能")
    print("=" * 60)
    
    test_url = "https://www.walmart.com/ip/test/123456"
    
    print("生成5组不同的请求头:")
    for i in range(5):
        headers = generate_random_headers(test_url)
        print(f"\n第{i+1}组请求头:")
        print(f"User-Agent: {headers.get('user-agent')}")
        print(f"Accept-Language: {headers.get('accept-language')}")
        print(f"DPR: {headers.get('dpr')}")
        print(f"Sec-CH-UA: {headers.get('sec-ch-ua')}")
        print(f"包含Referer: {'referer' in headers}")
        print(f"包含Downlink: {'downlink' in headers}")
        print(f"总头部数量: {len(headers)}")

def test_anti_bot_detection():
    """测试反爬虫检测功能"""
    print("\n" + "=" * 60)
    print("测试反爬虫检测功能")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            'name': 'PerimeterX检测',
            'response': '{"redirectUrl":"/blocked?url=Lw==\\u0026uuid=31fd0f0a-701b-11f0-8422-5e16fe108b7b","appId":"PXu6b0qd2S"}',
            'expected': ('PerimeterX', True)
        },
        {
            'name': '正常响应',
            'response': '<html><head><title>Walmart</title></head><body>Normal content</body></html>',
            'expected': (None, False)
        },
        {
            'name': 'Cloudflare检测',
            'response': '<html><head><title>Just a moment...</title></head><body>cf-ray: 123456</body></html>',
            'expected': ('Cloudflare', True)
        },
        {
            'name': '验证页面',
            'response': '<html><body>Activate and hold the button to confirm that you are human</body></html>',
            'expected': ('Verification', True)
        },
        {
            'name': '访问被拒绝',
            'response': '<html><body>Access Denied - You do not have permission</body></html>',
            'expected': ('AccessDenied', True)
        }
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['name']}")
        is_blocked, block_type, details = detect_anti_bot_response(case['response'], 'test_url')
        expected_type, expected_blocked = case['expected']
        
        print(f"  响应长度: {len(case['response'])} 字符")
        print(f"  检测结果: {'被阻止' if is_blocked else '正常'}")
        print(f"  阻止类型: {block_type}")
        print(f"  详细信息: {details}")
        print(f"  预期结果: {'被阻止' if expected_blocked else '正常'} ({expected_type})")
        
        # 验证结果
        if is_blocked == expected_blocked and (not expected_blocked or block_type == expected_type):
            print(f"  ✓ 测试通过")
        else:
            print(f"  ❌ 测试失败")

def test_header_diversity():
    """测试请求头多样性"""
    print("\n" + "=" * 60)
    print("测试请求头多样性")
    print("=" * 60)
    
    test_url = "https://www.walmart.com/ip/test/123456"
    user_agents = set()
    languages = set()
    dprs = set()
    
    # 生成100组请求头
    for i in range(100):
        headers = generate_random_headers(test_url)
        user_agents.add(headers.get('user-agent'))
        languages.add(headers.get('accept-language'))
        dprs.add(headers.get('dpr'))
    
    print(f"生成100组请求头的多样性统计:")
    print(f"  不同User-Agent数量: {len(user_agents)}")
    print(f"  不同Accept-Language数量: {len(languages)}")
    print(f"  不同DPR值数量: {len(dprs)}")
    
    print(f"\nUser-Agent样例:")
    for i, ua in enumerate(list(user_agents)[:3]):
        print(f"  {i+1}. {ua}")
    
    print(f"\nAccept-Language样例:")
    for i, lang in enumerate(list(languages)):
        print(f"  {i+1}. {lang}")

def save_test_results():
    """保存测试结果到文件"""
    print("\n" + "=" * 60)
    print("保存测试结果")
    print("=" * 60)
    
    test_url = "https://www.walmart.com/ip/test/123456"
    results = {
        'timestamp': datetime.now().isoformat(),
        'test_headers': []
    }
    
    # 生成10组测试请求头
    for i in range(10):
        headers = generate_random_headers(test_url)
        results['test_headers'].append({
            'index': i + 1,
            'headers': dict(headers)
        })
    
    # 保存到文件
    filename = f"test_headers_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"测试结果已保存到: {filename}")

def main():
    """主测试函数"""
    print("沃尔玛爬虫请求头测试工具")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行所有测试
        test_header_generation()
        test_anti_bot_detection()
        test_header_diversity()
        save_test_results()
        
        print("\n" + "=" * 60)
        print("✓ 所有测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

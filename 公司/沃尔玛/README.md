# 沃尔玛数据爬取程序

这是一个用于爬取沃尔玛商品数据的Python程序，支持定时任务功能。

## 功能特性

- 自动爬取沃尔玛商品信息（价格、库存、评分等）
- 支持定时任务，每天早上8点自动执行
- 数据存储到MySQL数据库
- 使用Redis进行缓存和错误记录
- 完善的错误处理和重试机制
- 支持URL标准化和验证

## 快速开始

### 1. 自动安装（推荐）
```bash
python install.py
```

### 2. 手动安装依赖
```bash
pip install -r requirements.txt
```

### 3. 验证安装
```bash
python test_scheduler.py --help
```

## 使用方法

### 1. 立即执行一次（默认模式）
```bash
python 沃尔玛.py
# 或者
python 沃尔玛.py --once
```

### 2. 启动定时任务
```bash
python 沃尔玛.py --schedule
# 或者使用专门的启动脚本
python start_scheduler.py
# Windows用户可以双击运行
start_scheduler.bat
```

### 3. 测试定时任务功能
```bash
# 每分钟执行一次测试
python test_scheduler.py --minute
# 测试每日定时任务（1分钟后执行）
python test_scheduler.py --daily
```

### 4. 查看帮助
```bash
python 沃尔玛.py --help
```

## 定时任务说明

- **执行时间**: 每天早上 8:00
- **运行方式**: 程序会持续运行，每分钟检查一次是否到了执行时间
- **停止方式**: 按 `Ctrl+C` 优雅停止程序
- **日志记录**: 所有执行过程都会记录详细日志

## 配置说明

程序需要配置以下数据库连接信息：

### MySQL数据库
- 主机: 120.25.103.16
- 端口: 3306
- 用户: sgm
- 密码: edfp.md4321
- 数据库: segmart_erp

### Redis缓存
- 主机: 120.25.103.16
- 端口: 6379
- 数据库: 2
- 密码: TezgyWhIxYHkBfci

## 数据表结构

程序使用以下两个数据表：

1. `sgm_walmart_item_subscribe` - 存储要爬取的商品URL
2. `sgm_walmart_item` - 存储爬取到的商品数据

## 注意事项

1. 确保数据库连接正常
2. 程序运行期间请保持网络连接稳定
3. 建议在服务器上使用 `nohup` 或 `screen` 运行定时任务
4. 定期检查日志文件，监控程序运行状态

## 后台运行

在Linux服务器上后台运行定时任务：

```bash
# 使用nohup后台运行
nohup python start_scheduler.py > walmart_scheduler.log 2>&1 &

# 或使用screen
screen -S walmart_scheduler
python start_scheduler.py
# 按 Ctrl+A+D 分离会话
```

## 故障排除

1. **数据库连接失败**: 检查数据库配置和网络连接
2. **Redis连接失败**: 检查Redis服务状态和配置
3. **爬取失败**: 检查目标网站是否有反爬虫措施
4. **定时任务不执行**: 检查系统时间和时区设置

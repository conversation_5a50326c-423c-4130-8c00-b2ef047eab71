#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
沃尔玛数据爬取定时任务启动脚本
"""

import os
import sys
import logging
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入主程序
from 沃尔玛 import run_scheduler

def main():
    """
    主函数
    """
    print("=" * 60)
    print("沃尔玛数据爬取定时任务启动器")
    print("=" * 60)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("任务调度: 每天早上 8:00 执行")
    print("按 Ctrl+C 停止程序")
    print("=" * 60)
    
    try:
        # 启动定时任务调度器
        run_scheduler()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logging.error(f"程序异常: {e}")
        print(f"程序异常: {e}")
    finally:
        print("程序已退出")

if __name__ == '__main__':
    main()

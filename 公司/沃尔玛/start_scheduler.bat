@echo off
chcp 65001 >nul
echo ============================================================
echo 沃尔玛数据爬取定时任务启动器
echo ============================================================
echo 启动时间: %date% %time%
echo 任务调度: 每天早上 8:00 执行
echo 按 Ctrl+C 停止程序
echo ============================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
pip show schedule >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 启动定时任务调度器...
echo.
python start_scheduler.py

echo.
echo 程序已退出
pause

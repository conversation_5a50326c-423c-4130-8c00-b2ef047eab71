import requests
import re
cookies = {
    'i18n_redirected': 'zh',
    'deviceid': 'b1f2c5b4cef073dc55b2a8f69cc5f8d',
    'Qs_lvt_404253': '**********',
    'Hm_lvt_d185b2974609101d8f9340b5f861ca70': '**********',
    'HMACCOUNT': '4C68CBB363B8EA3C',
    'Hm_lvt_8a5bd6e095cd118016489cab0443c2d7': '**********',
    'Hm_lvt_beac6fc75c36ba113cbffa9a59b1b18d': '**********',
    'showMajorDialog': 'true',
    '_ga': 'GA1.1.**********.**********',
    '_clck': 'fyan74%7C2%7Cfwy%7C0%7C1998',
    'token': '69417d5a8a00b8469c8715f16ce57117db05d3f1030b3107b4d495747d08194619d1433d4a80ac0687a6fd066ace4ea255c8155a0ae09b755ed88a9fc2b72f8f7380562e2aa9d7097ac229c7e13c0b7a',
    'Hm_lpvt_d185b2974609101d8f9340b5f861ca70': '**********',
    'Hm_lpvt_8a5bd6e095cd118016489cab0443c2d7': '**********',
    'Hm_lpvt_beac6fc75c36ba113cbffa9a59b1b18d': '**********',
    'Qs_pv_404253': '4287512293782021000%2C443090217133647740%2C4158728201982605000%2C1860682331155271400%2C4328747716425967000',
    '_uetsid': 'aeb55c704e9911f091a0c3d7ad449368',
    '_uetvid': 'aeb544104e9911f0b604177fb8bffb32',
    'mediav': '%7B%22eid%22%3A%*********%22%2C%22ep%22%3A%22%22%2C%22vid%22%3A%22U%5B%5DLASyI1R%3Dxrz%258ve%5B%5E%22%2C%22ctn%22%3A%22%22%2C%22vvid%22%3A%22U%5B%5DLASyI1R%3Dxrz%258ve%5B%5E%22%2C%22_mvnf%22%3A1%2C%22_mvctn%22%3A0%2C%22_mvck%22%3A0%2C%22_refnf%22%3A0%7D',
    '_ga_GVCWL6PNZ2': 'GS2.1.s1750517421$o3$g1$t1750519845$j34$l0$h0',
    '_clsk': '3yq0oy%7C1750519847355%7C9%7C1%7Cn.clarity.ms%2Fcollect',
}

headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'pragma': 'no-cache',
    'priority': 'u=0, i',
    'referer': 'https://app.diandian.com/login',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
    # 'cookie': 'i18n_redirected=zh; deviceid=b1f2c5b4cef073dc55b2a8f69cc5f8d; Qs_lvt_404253=**********; Hm_lvt_d185b2974609101d8f9340b5f861ca70=**********; HMACCOUNT=4C68CBB363B8EA3C; Hm_lvt_8a5bd6e095cd118016489cab0443c2d7=**********; Hm_lvt_beac6fc75c36ba113cbffa9a59b1b18d=**********; showMajorDialog=true; _ga=GA1.1.**********.**********; _clck=fyan74%7C2%7Cfwy%7C0%7C1998; token=69417d5a8a00b8469c8715f16ce57117db05d3f1030b3107b4d495747d08194619d1433d4a80ac0687a6fd066ace4ea255c8155a0ae09b755ed88a9fc2b72f8f7380562e2aa9d7097ac229c7e13c0b7a; Hm_lpvt_d185b2974609101d8f9340b5f861ca70=**********; Hm_lpvt_8a5bd6e095cd118016489cab0443c2d7=**********; Hm_lpvt_beac6fc75c36ba113cbffa9a59b1b18d=**********; Qs_pv_404253=4287512293782021000%2C443090217133647740%2C4158728201982605000%2C1860682331155271400%2C4328747716425967000; _uetsid=aeb55c704e9911f091a0c3d7ad449368; _uetvid=aeb544104e9911f0b604177fb8bffb32; mediav=%7B%22eid%22%3A%*********%22%2C%22ep%22%3A%22%22%2C%22vid%22%3A%22U%5B%5DLASyI1R%3Dxrz%258ve%5B%5E%22%2C%22ctn%22%3A%22%22%2C%22vvid%22%3A%22U%5B%5DLASyI1R%3Dxrz%258ve%5B%5E%22%2C%22_mvnf%22%3A1%2C%22_mvctn%22%3A0%2C%22_mvck%22%3A0%2C%22_refnf%22%3A0%7D; _ga_GVCWL6PNZ2=GS2.1.s1750517421$o3$g1$t1750519845$j34$l0$h0; _clsk=3yq0oy%7C1750519847355%7C9%7C1%7Cn.clarity.ms%2Fcollect',
}

response = requests.get('https://app.diandian.com/', cookies=cookies, headers=headers)

s = re.findall('u:\{s:"(.*?)"', response.text)

print("\n使用原始方法:")
print(f"s = {s[0] if s else None}")

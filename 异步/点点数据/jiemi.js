const crypto = require('crypto');
function fun2(e, n, o) {
    var d = "";
    n = Buffer.from(n, "utf8");
    o = Buffer.from(o, "utf8");
    var c = crypto.createDecipheriv("aes-128-cbc", n, o);
    return d += c.update(e, "hex", "utf8"),
    d += c.final("utf8")
}

function fun1(e, path, n, r) {
    var s = n.s
      , d = n.k
      , m = n.l
      , f = n.d
      , v = n.sort
      , l = n.num
      , k = function(content, t, e) {
        for (var a = Array.from(content), n = Array.from(t), r = a.length, o = n.length, d = String.fromCodePoint, i = 0; i < r; i++)
            a[i] = d(a[i].codePointAt(0) ^ n[(i + e) % o].codePointAt(0));
        return a.join("")
    }(function(s, t, path, e) {
        return [s, t, e, path].join("(&&)")
    }(function(t, e) {
        var n = t;
        if (true) {
            var r = [];
            for (var d in n)
                Array.isArray(n[d]) && "get" === e && (n[d] = n[d].join("")),
                "post" === e && (Array.isArray(n[d]) || o()(n[d])) && (n[d] = JSON.stringify(n[d])),
                r.push(n[d]);
            return r.sort(),
            r.join("")
        }
    }(e, r), parseInt((new Date).getTime() / 1e3) - 655876800 - f, path, v), fun2(s, d, m), l);
    return btoa(k)
}

r = {
    "market_id": 1,
    "rank_type": 1,
    "country_id": 75,
    "genre_id": 0,
    "device_id": 1,
    "time": 1750521211,
    "category_id": 0,
    "page": 4,
    "word": "",
    "min_index": "",
    "max_index": "",
    "min_popularity": "",
    "max_popularity": "",
    "min_bidding_apps_count": "",
    "max_bidding_apps_count": "",
    "min_results_count": "",
    "max_results_count": "",
    "page_size": ""
}
path = '/v1/word/rank/trend'

n = {
    "proxy": "/app",
    "target": "",
    "sort": "dd",
    "num": 10,
    "s": "61d9a605b0c92a317044b0dcedb35eee",
    "k": "2d0e43087ed44935",
    "l": "a321a82b9247979c",
    "d": -1
}

o = fun1(r, path, {
    s: n.s,
    k: n.k,
    l: n.l,
    d: n.d,
    sort: n.sort,
    num: n.num
}, "get");
console.log(o)
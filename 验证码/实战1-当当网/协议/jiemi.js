// 引入crypto-js库来实现MD5加密
const CryptoJS = require('crypto-js');

// MD5加密函数
J = function(t) {
    return CryptoJS.MD5(t).toString();
}

// 生成时间戳和随机数的函数
function permanent_id() {
    var t = "DDClick521"
        , e = new Date
        , n = e.getFullYear() + ""
        , a = e.getMonth() + 1;
    a < 10 && (a = "0" + a);
    var r = e.getDate();
    r < 10 && (r = "0" + r);
    var s = e.getHours();
    s < 10 && (s = "0" + s);
    var i = e.getMinutes();
    i < 10 && (i = "0" + i);
    var o = e.getSeconds();
    o < 10 && (o = "0" + o);
    var c = "00" + e.getMilliseconds();
    c = c.substr(c.length - 3, 3);
    var l = Math.floor(1e5 + 9e5 * Math.random())
        , u = Math.floor(1e5 + 9e5 * Math.random())
        , d = n + a + r + s + i + o + c + l + u + t
        , p = J(d)
        , h = function (t) {
        var e = function (t, e) {
            return new Array(e + 1).join(t)
        }
            , n = parseInt(t.substr(0, 8), 16)
            , a = String(n).substr(0, 6)
            , r = a.length;
        return r < 6 && (a += e("0", Math.abs(6 - r))),
            a
    };
    return p = h(p),
    n + a + r + s + i + o + c + p + l + u
}

// AES加密函数
Y = function(t, e) {
    U = CryptoJS.enc.Utf8.parse("0102030405060708")
    e = CryptoJS.enc.Utf8.parse(e);
    var n = CryptoJS.enc.Utf8.parse(t);
    return CryptoJS.AES.encrypt(n, e, {
        iv: U
    }).toString()
}

// 对象转查询字符串函数，替代NaNa.stringify
function objectToQueryString(obj) {
    // 获取对象所有键并排序
    const keys = Object.keys(obj).sort();
    
    // 构建查询字符串数组
    const queryParts = keys.map(key => {
        // 只处理非空值和非undefined值
        if (obj[key] !== undefined && (obj[key] !== null || obj[key] === 0)) {
            // 使用encodeURIComponent编码值
            return `${key}=${encodeURIComponent(obj[key])}`;
        }
        return '';
    }).filter(part => part !== ''); // 过滤掉空字符串
    
    // 用&连接所有部分
    return queryParts.join('&');
}

// 示例数据
n = {
    "t": 1751711287976,
    "ct": "pc",
    "permanent_id": "20250705174815524561575371861964347",
    "requestId": "2507051824258200k0SmIp_34f7"
}
n = Object.assign(n, undefined)
const a = {};
Object.keys(n).sort().forEach(function(key) {
    if (key !== "sign" && (n[key] || n[key] === 0)) {
        a[key] = n[key];
    }
});

// 使用我们的函数替代NaNa.stringify
var r = objectToQueryString(a);
console.log(r);
r = decodeURIComponent(r);
r = J(r);
r = Y(r, 'UauHoPKjEgWt8cjt');
console.log(r);

// 生成签名函数
function generateSign(params, key = 'UauHoPKjEgWt8cjt') {
    // 1. 将参数转换为查询字符串
    const queryString = objectToQueryString(params);
    
    // 2. 解码URL编码
    const decodedString = decodeURIComponent(queryString);
    
    // 3. 进行MD5加密
    const md5Hash = J(decodedString);
    
    // 4. 进行AES加密
    return Y(md5Hash, key);
}

// 导出函数以便被其他环境调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        J: J,
        permanent_id: permanent_id,
        objectToQueryString: objectToQueryString,
        generateSign: generateSign,
        Y: Y
    };
}


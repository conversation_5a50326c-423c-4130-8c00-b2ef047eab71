import json

import requests
import execjs
import os
import time
import random
import cv2
import logging

session = requests.Session()

# 获取JS上下文
def get_js_context():
    # 获取当前文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    js_file_path = os.path.join(current_dir, 'jiemi.js')
    
    # 读取JS文件内容
    with open(js_file_path, 'r', encoding='utf-8') as f:
        js_code = f.read()
    
    # 编译JS代码
    return execjs.compile(js_code)

# 生成permanent_id
def generate_permanent_id():
    ctx = get_js_context()
    return ctx.call('permanent_id')

# 获取当前时间戳（毫秒）
def get_current_timestamp():
    return int(time.time() * 1000)

# 生成请求ID
def generate_request_id():
    # 格式: MMDDHHMMSS + 随机字符串
    now = time.localtime()
    date_part = f"{now.tm_mon:02d}{now.tm_mday:02d}{now.tm_hour:02d}{now.tm_min:02d}{now.tm_sec:02d}"
    random_part = ''.join([chr(ord('a') + int(random.random() * 26)) for _ in range(10)])
    return f"{date_part}{random_part}"

# 生成sign签名
def generate_sign(params, key='UauHoPKjEgWt8cjt'):
    ctx = get_js_context()
    return ctx.call('generateSign', params, key)


def generate_point_json(params, key='UauHoPKjEgWt8cjt'):
    ctx = get_js_context()
    return ctx.call('Y', params, key)


# 获取默认请求头
def get_headers():
    return {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://login.dangdang.com',
        'Pragma': 'no-cache',
        'Referer': 'https://login.dangdang.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

# 获取随机密钥
def get_rankey(permanent_id=None, request_id=None):
    """
    获取随机密钥
    
    Args:
        permanent_id: 可选，指定permanent_id，如不提供则自动生成
        request_id: 可选，指定requestId，如不提供则自动生成
        
    Returns:
        Response: requests响应对象
    """
    # 如果未提供permanent_id，则自动生成
    if permanent_id is None:
        permanent_id = generate_permanent_id()
        
    # 获取当前时间戳
    current_timestamp = get_current_timestamp()
    
    # 如果未提供requestId，则自动生成
    if request_id is None:
        now = time.localtime()
        date_part = f"{now.tm_mon:02d}{now.tm_mday:02d}{now.tm_hour:02d}{now.tm_min:02d}{now.tm_sec:02d}"
        random_part = ''.join([chr(ord('a') + int(random.random() * 26)) for _ in range(10)])
        request_id = f"{date_part}{random_part}"
    
    # 构建请求数据
    data = {
        't': str(current_timestamp),
        'ct': 'pc',
        'permanent_id': permanent_id,
        'requestId': request_id
    }
    
    # 生成sign
    sign = generate_sign(data)
    
    # 添加sign到请求数据
    data['sign'] = sign
    
    # 发送请求
    response = session.post(
        'https://login.dangdang.com/api/customer/loginapi/getRankey', 
        headers=get_headers(), 
        data=data
    )
    
    return response.json()

def get_images(permanent_id,requestId,rankey):
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://login.dangdang.com',
        'Pragma': 'no-cache',
        'Referer': 'https://login.dangdang.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        # 'Cookie': '__permanent_id=20250705174815524561575371861964347; __visit_id=20250705174815592151741305181590307; __out_refer=; __rpm=%7Clogin_page...1751708920714; __trace_id=20250705174840833405983401102690634; JSESSIONID=C3B9B0E3299C3CA27FF221022C1C17B2',
    }

    # 获取当前时间戳
    current_timestamp = get_current_timestamp()

    data = {
        't': str(current_timestamp),
        'ct': 'pc',
        'permanent_id': permanent_id,
        'requestId': requestId,
        'situation': 'login'
    }

    # 生成sign
    sign = generate_sign(data,rankey)

    # 添加sign到请求数据
    data['sign'] = sign


    response = session.post(
        'https://login.dangdang.com/api/customer/loginapi/getSlidingVerifyCode',
        headers=headers,
        data=data,
    )
    return response.json()


def download_image(url, filename):
    """
    下载图片到本地
    """
    try:
        # 添加请求头以避免反爬虫
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://login.dangdang.com/'
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        with open(filename, 'wb') as f:
            f.write(response.content)
        print(f"✅ 图片已保存: {filename}")
        return True
    except Exception as e:
        print(f"❌ 下载图片失败: {e}")
        return False

def checkSlidingVerifyCode(permanent_id,requestId,verifyToken,encryptKey,left_x,left_y,rankey):
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://login.dangdang.com',
        'Pragma': 'no-cache',
        'Referer': 'https://login.dangdang.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }
    # 获取当前时间戳
    current_timestamp = get_current_timestamp()

    distance = int(left_x * 350 / 408)
    distance_data = {
        "x": distance / 350,
        "y": left_y
    }
    point_data = json.dumps(distance_data,separators=(',',':'))


    point_json = generate_point_json(point_data,encryptKey)


    data = {
        't': str(current_timestamp),
        'ct': 'pc',
        'permanent_id': permanent_id,
        'requestId': requestId,
        'situation': 'login',
        'verifyToken': verifyToken,
        'slide_cost_time': 1426,
        'need_new_verifydata': 0,
        'point_json': point_json
    }

    # 生成sign
    sign = generate_sign(data,rankey)

    # 添加sign到请求数据
    data.update({
        "sign": sign,
    })

    print(data)

    cookies = {
        '__permanent_id': permanent_id
    }

    response = session.post(
        'https://login.dangdang.com/api/customer/loginapi/checkSlidingVerifyCode',
        headers=headers,
        data=data,
        cookies=cookies
    )
    print(response.json())

def max_loc():
    # 读取背景图和滑块图
    gap_img = cv2.imread('bg.jpg')  # 背景图
    slide_img = cv2.imread('slide.png')  # 滑块图

    # 使用 Canny 算法进行边缘检测
    gap_edge = cv2.Canny(gap_img, 200, 300)
    slide_edge = cv2.Canny(slide_img, 200, 300)

    # 将边缘图转换为 RGB 格式（虽然是单通道，转换后是 3 通道）
    gap_pic = cv2.cvtColor(gap_edge, cv2.COLOR_GRAY2RGB)
    slide_pic = cv2.cvtColor(slide_edge, cv2.COLOR_GRAY2RGB)

    # 模板匹配
    res1 = cv2.matchTemplate(gap_pic, slide_pic, cv2.TM_CCOEFF_NORMED)

    # 获取匹配结果中的最大值、最大值位置等
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res1)

    # 取出匹配到的位置的 x 坐标（滑块应该移动到这个位置）
    res = max_loc[0]

    return res

# 示例使用
if __name__ == "__main__":
    # 生成permanent_id
    permanent_id = generate_permanent_id()
    print(f"生成的permanent_id: {permanent_id}")
    
    # 获取随机密钥
    result = get_rankey()
    requestId = result['requestId']
    rankey = result['rankey']
    img_result = get_images(permanent_id,requestId,rankey)

    # 下载图片
    slideImg = img_result['data']['slideImg']
    bgImg = img_result['data']['bgImg']
    download_image(slideImg, "slide.png")
    download_image(bgImg, "bg.jpg")

    left_x = max_loc()


    heightRatio = img_result['data']['heightRatio']
    encryptKey = img_result['data']['encryptKey']
    token = img_result['data']['token']
    left_y = img_result['data']['y']
    checkSlidingVerifyCode(permanent_id=permanent_id,requestId=requestId,verifyToken=token,encryptKey=encryptKey,left_x=left_x,left_y=left_y,rankey=rankey)

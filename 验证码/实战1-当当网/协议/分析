1. 获取图片接口/loginapi/getSlidingVerifyCode
分析这三个参数获取图片
permanent_id 完成
requestId 是由/customer/loginapi/getRankey这个接口返回
sign

响应:
{
    "errorCode": "0",
    "errorMsg": "成功",
    "data": {
        "slideImg": "https://slide.ddimg.cn/3a925e6c9a2f4870b8099e36b3ae8faa.png",
        "y": 0.3529412,
        "bgImg": "https://slide.ddimg.cn/948ae7235b334c2085eb4244715e4482.jpg",
        "heightRatio": 0.3480392,
        "encryptKey": "LkAeEEt46RRrR1Mq",
        "token": "bf66f5abf1684fc69c0a0fefebeb1d79"
    },
    "status": "0"
}

---------------------------------------------------------------------
    1.1 /customer/loginapi/getRankey 接口 是加密sign aes 的key
        {
            "statusCode": "0",
            "errorCode": "0",
            "errorMsg": null,
            "requestId": "25070517481701603Vc8k4_d1f2",
            "rankey": "YMXS9IXWR6ap9Fff"
        }

---------------------------------------------------------------------
2.校验接口/loginapi/checkSlidingVerifyCode
t 时间戳

ct 写死

permanent_id 生成

requestId /loginapi/getRankey返回

situation 写死

verifyToken  获取图片接口返回token

slide_cost_time 写死

need_new_verifydata 写死

point_json 不清楚

sign 前面参数生成

# 当当网验证码加密解析

这个文件夹包含了当当网验证码系统中使用的加密算法实现。

## 文件说明

- `jiemi.js` - 实现了当当网验证码系统中的加密算法，包括MD5加密、AES加密、查询字符串生成和permanent_id生成函数
- `jiemi.py` - Python脚本，封装了一系列函数，通过PyExecJS调用JavaScript中的加密函数

## 运行方法

### 直接运行JavaScript

1. 首先安装依赖：

```bash
npm install crypto-js
```

2. 然后运行脚本：

```bash
node jiemi.js
```

### 通过Python调用JavaScript

1. 安装Python依赖：

```bash
pip install requests PyExecJS
```

2. 安装Node.js依赖：

```bash
npm install crypto-js
```

3. 运行Python脚本：

```bash
python jiemi.py
```

## 功能说明

该项目实现了当当网验证码系统中的加密逻辑，主要包含：

- JavaScript部分：
  - `J(t)`: MD5加密函数
  - `permanent_id()`: 生成带有时间戳和随机数的加密字符串
  - `Y(t, e)`: AES加密函数
  - `objectToQueryString(obj)`: 将对象转换为查询字符串
  - `generateSign(params, key)`: 生成请求签名

- Python部分 - 封装函数：
  - `get_js_context()`: 获取JavaScript执行环境
  - `generate_permanent_id()`: 调用JavaScript生成permanent_id
  - `get_current_timestamp()`: 获取当前时间戳（毫秒）
  - `generate_request_id()`: 生成请求ID
  - `generate_sign(params, key)`: 生成请求签名
  - `get_headers()`: 获取默认请求头
  - `get_rankey(permanent_id=None, request_id=None)`: 获取随机密钥

## 签名生成流程

当当网API的签名生成流程如下：

1. 对请求参数按键名排序
2. 将参数转换为查询字符串格式 `key1=value1&key2=value2&...`
3. 对查询字符串进行URL解码
4. 对解码后的字符串进行MD5加密
5. 对MD5加密结果进行AES加密，使用密钥 `UauHoPKjEgWt8cjt` 和IV `0102030405060708`

## 使用示例

```python
# 直接导入函数使用
from jiemi import generate_permanent_id, generate_sign, get_rankey

# 生成permanent_id
permanent_id = generate_permanent_id()
print(f"生成的permanent_id: {permanent_id}")

# 获取随机密钥
response = get_rankey()
print(f"响应状态码: {response.status_code}")
print(f"响应内容: {response.text}")

# 手动构建参数并生成签名
params = {
    't': '1751711287976',
    'ct': 'pc',
    'permanent_id': '20250705174815524561575371861964347',
    'requestId': '2507051824258200k0SmIp_34f7'
}
sign = generate_sign(params)
print(f"生成的签名: {sign}")
```

## 注意事项

1. 确保已安装Node.js和npm
2. 确保已安装Python和必要的依赖包
3. 如果遇到编码问题，请确保文件以UTF-8编码保存 
from DrissionPage import ChromiumOptions, ChromiumPage
import time
import requests
import os
from datetime import datetime
import cv2
import numpy as np
import random

def download_image(url, filename):
    """
    下载图片到本地
    """
    try:
        # 添加请求头以避免反爬虫
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://login.dangdang.com/'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            f.write(response.content)
        print(f"✅ 图片已保存: {filename}")
        return True
    except Exception as e:
        print(f"❌ 下载图片失败: {e}")
        return False

def get_track(distance):
    """
    生成人性化的滑动轨迹 - 明显加速减速模式
    """
    track = []
    current = 0
    
    # 设定移动步数（通常10-15步）
    total_steps = random.randint(10, 15)
    
    # 生成加速减速序列
    accelerate_steps = total_steps // 2  # 前半段加速
    decelerate_steps = total_steps - accelerate_steps  # 后半段减速
    
    # 加速阶段：从小到大
    max_speed = random.randint(8, 12)  # 最大速度随机化
    for i in range(accelerate_steps):
        # 计算当前速度（线性加速）
        speed = int(max_speed * (i + 1) / accelerate_steps)
        # 添加小随机变化
        speed += random.randint(-1, 1)
        speed = max(1, speed)  # 确保速度至少为1
        track.append(speed)
        current += speed
    
    # 减速阶段：从大到小
    for i in range(decelerate_steps):
        # 计算当前速度（线性减速）
        speed = int(max_speed * (decelerate_steps - i) / decelerate_steps)
        # 添加小随机变化
        speed += random.randint(-1, 1)
        speed = max(1, speed)  # 确保速度至少为1
        track.append(speed)
        current += speed
    
    # 调整轨迹以匹配目标距离
    scale_factor = distance / current if current > 0 else 1
    
    # 重新计算轨迹，确保总距离正确
    adjusted_track = []
    adjusted_current = 0
    
    for move in track:
        adjusted_move = round(move * scale_factor)
        adjusted_move = max(1, adjusted_move)  # 确保每步至少移动1像素
        adjusted_track.append(adjusted_move)
        adjusted_current += adjusted_move
    
    # 微调最后几步以精确匹配目标距离
    diff = distance - adjusted_current
    if diff > 0:
        # 距离不够，增加最后几步
        for i in range(min(abs(diff), len(adjusted_track))):
            adjusted_track[-(i+1)] += 1
    elif diff < 0:
        # 距离超了，减少最后几步
        for i in range(min(abs(diff), len(adjusted_track))):
            if adjusted_track[-(i+1)] > 1:
                adjusted_track[-(i+1)] -= 1
    
    print(f"🎢 轨迹模式: {adjusted_track}")
    return adjusted_track

def get_human_track(distance):
    """
    生成更加人性化的滑动轨迹，包含垂直偏移
    """

    # 水平轨迹（使用新的加速减速模式）
    h_track = get_track(distance)
    
    # 生成垂直轨迹（模拟人手抖动）
    v_track = []
    for i, x_move in enumerate(h_track):
        # 添加随机的垂直偏移，但总体趋势回归到0
        progress = i / len(h_track)
        
        if progress < 0.3:
            # 前30%：可能有较大偏移（模拟起步不稳）
            v_offset = random.randint(-3, 4)
        elif progress < 0.7:
            # 中间40%：中等偏移（模拟拖拽过程）
            v_offset = random.randint(-2, 3)
        else:
            # 后30%：逐渐回归到水平线（模拟收尾精确）
            v_offset = random.randint(-1, 1)
        
        v_track.append(v_offset)
    
    # 组合成坐标对
    track_points = [(h_track[i], v_track[i]) for i in range(len(h_track))]
    
    # 打印轨迹信息
    print(f"🎯 水平轨迹: {[point[0] for point in track_points]}")
    print(f"📏 垂直偏移: {[point[1] for point in track_points]}")
    
    return track_points

def move_slider(page, distance):
    """
    移动滑块到指定位置
    """
    try:
        # 查找滑块元素 - 只使用id="sliderBtn"
        slider = page.ele('#sliderBtn')
        
        if not slider:
            print("❌ 未找到滑块元素")
            return False
        
        distance = int(distance*350 / 408)
        print(f"🎯 找到滑块元素，准备移动到位置: {distance}")
        
        # 使用人性化轨迹一次性拖拽滑块
        try:
            print("🔄 开始人性化滑块验证...")
            
            # 生成人性化轨迹
            track_points = get_human_track(distance)
            print(f"📈 生成人性化轨迹: {len(track_points)}个移动点")
            
            # 计算总的偏移量（包含轨迹中的随机偏移）
            total_x = sum(point[0] for point in track_points)
            total_y = sum(point[1] for point in track_points)
            
            # 添加轨迹的随机性
            final_x = total_x + random.randint(-3, 3)  # 最终位置添加小随机偏移
            final_y = total_y + random.randint(-2, 2)  # 垂直方向小幅偏移
            
            print(f"🎯 目标偏移: x={final_x}, y={final_y}")
            
            # 使用变化的拖拽时长（模拟人类操作快慢不一）
            drag_duration = random.uniform(1.2, 2.5)
            print(f"⏱️ 拖拽时长: {drag_duration:.2f}秒")
            
            # 执行一次性拖拽操作
            slider.drag(final_x, final_y, duration=drag_duration)
            
            print("✅ 滑块验证拖拽完成")
            
            # 等待验证码响应
            time.sleep(0.8)
            
        except Exception as e:
            print(f"❌ 滑块验证拖拽失败: {e}")
            
            # 备用方案：使用基础距离直拖
            try:
                print("🔄 使用直拖备用方案...")
                # 添加小的随机偏移避免精确匹配
                backup_x = distance + random.randint(-5, 5)
                backup_y = random.randint(-3, 3)
                backup_duration = random.uniform(1.5, 2.8)
                
                slider.drag(backup_x, backup_y, duration=backup_duration)
                print("✅ 备用方案执行完成")
                time.sleep(0.5)
                
            except Exception as e2:
                print(f"❌ 备用方案也失败: {e2}")
                return False
        
        print("✅ 滑块移动完成!")
        return True
        
    except Exception as e:
        print(f"❌ 移动滑块时出错: {e}")
        return False

def get_captcha_images(page, save_dir="./"):
    """
    获取验证码图片
    """
    bg_downloaded = False
    slide_downloaded = False
    
    try:
        # 创建保存目录
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        bg_filename = os.path.join(save_dir, f"bg.jpg")
        slide_filename = os.path.join(save_dir, f"slide.png")
        
        print("🔍 正在查找验证码图片...")
        
        # 查找背景图片元素 - 多种方式尝试
        bg_img = None
        bg_selectors = [
            '#bgImg',
            'img[id="bgImg"]',
            'img.img',
            'img[data-v-72dfa45e][id="bgImg"]',
            'img[src*="slide.ddimg.cn"]'
        ]
        
        for selector in bg_selectors:
            bg_img = page.ele(selector)
            if bg_img:
                break
        
        if bg_img:
            bg_src = bg_img.attr('src')
            if bg_src:
                print(f"🖼️  背景图片URL: {bg_src}")
                bg_downloaded = download_image(bg_src, bg_filename)
            else:
                print("❌ 背景图片URL为空")
        else:
            print("❌ 未找到背景图片元素")
        
        # 查找滑块图片元素
        slide_img = page.ele('#simg')
        
        if slide_img:
            slide_src = slide_img.attr('src')
            if slide_src:
                print(f"🧩 滑块图片URL: {slide_src}")
                slide_downloaded = download_image(slide_src, slide_filename)
                
                # 获取滑块位置信息
                style = slide_img.attr('style')
                if style:
                    print(f"📍 滑块样式: {style}")
            else:
                print("❌ 滑块图片URL为空")
        else:
            print("❌ 未找到滑块图片元素")
        
        # 输出所有img标签用于调试
        print("\n🔍 页面中的所有img标签:")
        all_imgs = page.eles('img')
        for i, img in enumerate(all_imgs, 1):
            src = img.attr('src')
            id_attr = img.attr('id')
            class_attr = img.attr('class')
            print(f"  {i}. ID: {id_attr}, Class: {class_attr}, Src: {src}")
        
        return bg_downloaded, slide_downloaded, bg_filename if bg_downloaded else None, slide_filename if slide_downloaded else None
        
    except Exception as e:
        print(f"❌ 获取验证码图片时出错: {e}")
        return False, False, None, None

def login_with_captcha_detection():
    """
    登录并检测验证码
    """
    co = ChromiumOptions()
    co.headless(False)
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    page = ChromiumPage(co)
    
    try:
        print("🌐 正在访问当当网登录页面...")
        page.get('https://login.dangdang.com/#')
        time.sleep(3)
        
        print("📝 正在填写登录信息...")
        # 填写用户名
        username_input = page.ele("x://*[text()='手机号/昵称/邮箱']/following-sibling::input")
        if username_input:
            username_input.input('13800138000')
            print("✅ 已填写用户名")
        
        # 填写密码
        password_input = page.ele("x://*[text()='密码']/following-sibling::input")
        if password_input:
            password_input.input('123456')
            print("✅ 已填写密码")
        
        # 同意协议
        agree_checkbox = page.ele("x://input[@title='同意用户协议、隐私政策选择框']")
        if agree_checkbox:
            agree_checkbox.click()
            print("✅ 已同意用户协议")
        
        time.sleep(1)
        
        # 点击登录按钮
        login_button = page.ele('x://*[@class="btn"]')
        if login_button:
            login_button.click()
            print("✅ 已点击登录按钮")
        
        print("⏳ 等待验证码加载...")
        time.sleep(3)
        
        # 获取验证码图片
        bg_success, slide_success, bg_file, slide_file = get_captcha_images(page)
        
        print("🎉 验证码图片获取成功!")
        print(f"   背景图片: {bg_file}")
        print(f"   滑块图片: {slide_file}")

        # 读取背景图和滑块图
        gap_img = cv2.imread(bg_file)        # 背景图
        slide_img = cv2.imread(slide_file)   # 滑块图

        # 使用 Canny 算法进行边缘检测
        gap_edge = cv2.Canny(gap_img, 200, 300)
        slide_edge = cv2.Canny(slide_img, 200, 300)

        # 将边缘图转换为 RGB 格式（虽然是单通道，转换后是 3 通道）
        gap_pic = cv2.cvtColor(gap_edge, cv2.COLOR_GRAY2RGB)
        slide_pic = cv2.cvtColor(slide_edge, cv2.COLOR_GRAY2RGB)

        # 模板匹配
        res1 = cv2.matchTemplate(gap_pic, slide_pic, cv2.TM_CCOEFF_NORMED)

        # 获取匹配结果中的最大值、最大值位置等
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res1)

        # 取出匹配到的位置的 x 坐标（滑块应该移动到这个位置）
        res = max_loc[0]

        # 输出结果
        print(f"🎯 滑块应该移动到的位置 x 坐标是: {res}")
        
        # 移动滑块
        success = move_slider(page, res)
        
        if success:
            print("✅ 滑块移动成功，等待验证结果...")
            time.sleep(3)
            
            # 检查是否验证成功
            current_url = page.url
            if 'login' not in current_url:
                print("🎉 登录成功！")
                print(f"当前页面: {current_url}")
            else:
                print("⚠️ 可能需要重新验证")
        else:
            print("❌ 滑块移动失败")
        
        # 保持浏览器打开
        print("\n🔄 浏览器将保持打开状态，请观察结果...")
        print("👆 按 Enter 键关闭浏览器...")
        input()
        
    except Exception as e:
        print(f"❌ 登录过程中出错: {e}")
    
    finally:
        page.quit()
        print("🔚 浏览器已关闭")

if __name__ == "__main__":
    login_with_captcha_detection() 
import cv2

# 读取背景图和滑块图
gap_img = cv2.imread('bg.jpg')        # 背景图
slide_img = cv2.imread('slide.jpg')   # 滑块图

# 使用 Canny 算法进行边缘检测
gap_edge = cv2.Canny(gap_img, 200, 300)
slide_edge = cv2.Canny(slide_img, 200, 300)

# 将边缘图转换为 RGB 格式（虽然是单通道，转换后是 3 通道）
gap_pic = cv2.cvtColor(gap_edge, cv2.COLOR_GRAY2RGB)
slide_pic = cv2.cvtColor(slide_edge, cv2.COLOR_GRAY2RGB)

# 模板匹配
res1 = cv2.matchTemplate(gap_pic, slide_pic, cv2.TM_CCOEFF_NORMED)

# 获取匹配结果中的最大值、最大值位置等
min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res1)

# 取出匹配到的位置的 x 坐标（滑块应该移动到这个位置）
res = max_loc[0]

# 输出结果
print(f"滑块应该移动到的位置 x 坐标是: {res}")

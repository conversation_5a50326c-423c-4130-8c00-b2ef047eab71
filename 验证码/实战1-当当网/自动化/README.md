# 当当网登录页面访问 + 滑块验证码自动破解

使用DrissionPage访问当当网登录页面，获取滑块验证码图片，并自动计算移动距离完成滑块验证的完整解决方案。

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 运行完整的自动化登录程序

```bash
python captcha_handler.py
```

## 功能特点

### 🚀 核心功能
- ✅ 自动访问当当网登录页面
- ✅ 自动填写用户名和密码
- ✅ 自动点击同意协议
- ✅ 自动点击登录按钮
- ✅ 设置User-Agent避免被识别为爬虫

### 🔧 验证码处理功能
- ✅ **自动检测滑块验证码**
- ✅ **获取背景图片URL并下载**
- ✅ **获取滑块图片URL并下载**
- ✅ **使用OpenCV进行图像处理**
- ✅ **Canny边缘检测算法**
- ✅ **模板匹配计算滑块位置**
- ✅ **多种选择器策略确保元素获取**

### 🎯 滑块移动功能
- ✅ **自动计算滑块移动距离**
- ✅ **生成人性化滑动轨迹**
- ✅ **模拟真实用户操作**
- ✅ **智能加速减速算法**
- ✅ **随机延迟避免检测**

## 技术栈

| 技术 | 用途 |
|------|------|
| DrissionPage | 浏览器自动化 |
| OpenCV | 图像处理和模板匹配 |
| NumPy | 数值计算 |
| Requests | 图片下载 |

## 算法原理

### 1. 图像处理流程
```
原始图片 → Canny边缘检测 → 模板匹配 → 获取位置坐标
```

### 2. 滑块移动轨迹算法
```python
def get_track(distance):
    """
    生成人性化的滑动轨迹
    - 前4/5距离：加速运动
    - 后1/5距离：减速运动
    """
    # 物理运动公式：v = v0 + at, x = v0t + 1/2 * a * t^2
```

### 3. 验证码破解流程
```
1. 获取验证码图片 → 2. 边缘检测 → 3. 模板匹配 → 4. 计算位置 → 5. 生成轨迹 → 6. 移动滑块
```

## 验证码图片获取

### 自动获取的图片
1. **背景图片** (`bg.jpg`)
   - 包含缺口的完整背景图
   - 用于计算滑块移动距离

2. **滑块图片** (`slide.png`)
   - 需要拖动的滑块图片
   - 用于图像匹配算法

## 核心代码功能

### 🖼️ 图像处理
```python
# 边缘检测
gap_edge = cv2.Canny(gap_img, 200, 300)
slide_edge = cv2.Canny(slide_img, 200, 300)

# 模板匹配
res1 = cv2.matchTemplate(gap_pic, slide_pic, cv2.TM_CCOEFF_NORMED)
min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res1)
```

### 🎯 滑块移动
```python
# 生成轨迹
track = get_track(distance)

# 模拟拖动
page.actions.move_to(slider_center_x, slider_center_y).hold()
for move in track:
    page.actions.move_to(new_x, new_y, duration=0.1)
page.actions.release()
```

## 程序执行流程

1. 🌐 **访问登录页面**
2. 📝 **自动填写登录信息**
3. 🔘 **点击登录按钮**
4. ⏳ **等待验证码加载**
5. 🔍 **检测并下载验证码图片**
6. 🖼️ **使用OpenCV进行图像处理**
7. 🎯 **计算滑块移动距离**
8. 📈 **生成人性化滑动轨迹**
9. 🖱️ **执行滑块拖动操作**
10. ✅ **检查验证结果**

## 特色功能

### 🧠 智能算法
- **边缘检测**: 使用Canny算法提取图像边缘特征
- **模板匹配**: 使用OpenCV模板匹配算法精确定位
- **轨迹生成**: 模拟真实用户的加速减速过程
- **随机延迟**: 避免被识别为机器人操作

### 🔧 技术优化
- **多选择器策略**: 确保元素获取的稳定性
- **完整错误处理**: 详细的错误信息和异常处理
- **调试模式**: 显示页面中所有img元素用于调试
- **防反爬虫**: 合适的请求头和User-Agent设置

## 运行示例

```bash
$ python captcha_handler.py

🌐 正在访问当当网登录页面...
📝 正在填写登录信息...
✅ 已填写用户名
✅ 已填写密码
✅ 已同意用户协议
✅ 已点击登录按钮
⏳ 等待验证码加载...
🔍 正在查找验证码图片...
🖼️ 背景图片URL: https://slide.ddimg.cn/xxx.jpg
✅ 图片已保存: bg.jpg
🧩 滑块图片URL: https://slide.ddimg.cn/xxx.png
✅ 图片已保存: slide.png
🎯 滑块应该移动到的位置 x 坐标是: 165
🎯 找到滑块元素，准备移动到位置: 165
📏 滑块位置: Rectangle(x=123, y=456, width=60, height=60)
📈 生成拖动轨迹: [2, 4, 6, 8, 10, 12, 14, 16, 18, 20]...
🎯 滑块中心位置: (153, 486)
✅ 滑块移动完成!
✅ 滑块移动成功，等待验证结果...
🎉 登录成功！
```

## 注意事项

1. 确保已安装Chrome浏览器
2. 网络连接稳定以便下载图片
3. 验证码图片会保存在当前目录
4. 可以根据需要调整Canny检测参数
5. 滑动轨迹算法可根据实际情况调整

## 技术扩展

可以基于此代码进一步开发：
- 🔧 调整图像处理参数提高识别率
- 🎯 优化滑动轨迹算法
- 🤖 集成机器学习模型
- 📊 添加成功率统计功能 
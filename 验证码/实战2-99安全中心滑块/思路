地址:https://aq.99.com/V3/NDUser_Login.htm

第一步获取验证码图片
import requests
data = '{"action":"create","challenge":"eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJORF9DSEVDS19DT0RFIiwidGlja2V0IjoiM2UxNjUzNWI1Y2YzMmRmZjdmODIzMTFmYzE3YjU5NWIiLCJleHAiOjE3NTMzNTM1NjYsImlhdCI6MTc1MzM1Mjk2NiwianRpIjoiZjNjZGM0MGEtNGJjMC00OTFjLWJmZWEtMDBjMjljZWIzNGUzIn0.sPhphYM9ZV8NijF-JsW-5OtxsUfeWfajZPko0SGyeU0","ts":1753352965392}'
challenge
action 固定
ts 时间戳
https://checkcode.99.com/slide

第二步获取返回challenge
data = {
    'Action': 'checkcodeinit',
    'Data': 'c5690a007268129172c31e5e8b070fcc',
    'Business': 'common',
}
response = requests.post('https://aq.99.com/V5/Handler/Default.ashx', cookies=cookies, headers=headers, data=data)


第三步还原地图
def restore_image_from_dict(params: dict,image_path,output_path):
    """
    模拟 JS 的 Mt 方法还原图像

    参数:
        params = {
            "image_path": str,                # 被打乱的图像路径
            "image_seg_length": int,          # 每段宽度
            "image_random_pos": List[int],    # 顺序映射列表
            "original_width": int,            # 原图宽度
            "original_height": int            # 原图高度
        }

    返回:
        PIL.Image: 还原后的图像
    """
    # 提取参数
    seg_len = params["imageSegLength"]
    pos_list = params["imageRandomPos"]
    width = params["originalImageWidth"]
    height = params["originalImageHeight"]

    g = len(pos_list)

    scrambled = Image.open(image_path).convert("RGB")
    canvas = Image.new("RGB", (width, height))

    d = 0  # 从左到右裁剪起点

    for v, h in enumerate(pos_list):
        p = h * seg_len  # 放置在还原图上的位置
        y = seg_len if h != g - 1 else width - seg_len * (g - 1)  # 最后一段宽度

        # 从 scrambled 裁剪图像段（d 是当前拼图的位置）
        box = (d, 0, d + y, height)
        seg = scrambled.crop(box)

        # 把它放到 canvas 上 p 的位置
        canvas.paste(seg, (p, 0))

        d += y  # 更新下一个段的起点
    canvas.save(output_path)
    return output_path
import requests

cookies = {
    'OCSESSID': 'a759f9e20790db964458f659af',
}

headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'origin': 'https://www.gigab2b.com',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://www.gigab2b.com/index.php?route=account/customer_order',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
    'x-requested-with': 'XMLHttpRequest, XMLHttpRequest',
    # 'cookie': '_pk_id.2.185c=2b54f6fb9a5b1779.**********.; _ga=GA1.1.*********.**********; country=USA; currency=USD; _gcl_au=1.1.********.**********; gmd_device_id=5474a9b9-167a-4c3c-9067-4680fa6ba244; acw_tc=ac11000117514682691452636e00bbbd2cee8ecbcc8f16f40f8403de618d41; acw_sc__v2=686548ee369d00d6064aefe689351753128a0471; _ayo=3f89884d890d87701c8458d367a9e2e8; OCSESSID=eb7401d44d6259a62bcb486929; tfstk=gz1sKvVKucm6HIdxld4EOMc8z8Ob8yPzCqTArZhZkCdtDmQJYG7N_RvXpitF_h7w6S6fqgi2HI5VkVfCVjlajRbxGIRY4uPzaNbwiIEyaf7_6VY2lAHt0EppvI8jlWQ_tNbMi2q-B-W1SqsPOZA9MidppEL6MFHODBhpxEYxXEhTRytHlEL9HdnL9FYoWIdADwUBoHKvMdIAJvntAEy6kNawsyybxq8NWHGxM1FDC3tq3jGf9F91MNKC5N1B5dtWLPVR81IFk15MKuiJiwW5ctI0UvA1kOd9zNVsOQQOLsTFgrkXXGfCJ1vIrbAfBG6vE9NoEdpXf9C9dqhA-B6CDts_fAxOQM9kJp3bZeSyvNfOdrmdWg-66epUwz_pH95ME1rK1QBl7CWAm8kH1ZB9Og8ma3N-JxgBEjTB4yaInxq_O_CK6Cn6NdLHW7zQRDMDBeYB4yaInxv9-FBURyiIn; __hs_opt_out=no; __hs_initial_opt_in=true; __hstc=*********.9c95077bf0e43c29969b30984f2f9ad2.*************.*************.*************.1; hubspotutk=9c95077bf0e43c29969b30984f2f9ad2; __hssrc=1; _fbp=fb.1.*************.499128740199461330; login_flag=1; is_partner=0; _ga_39N4BF4XSG=GS2.1.s1751468270$o10$g1$t1751469573$j12$l0$h0; __hssc=*********.4.*************',
}

data = {
    'page_limit': '15',
}

response = requests.post(
    'https://www.gigab2b.com/index.php?route=account/customer_order/getLabelViewInfo',
    cookies=cookies,
    headers=headers,
    data=data,
)
print(response.text)
import requests

cookies = {
    'OCSESSID': '82e51575fba4ec344ea36c749b',
}

headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'ori-status-in-response': 'code',
    'priority': 'u=1, i',
    'referer': 'https://www.gigab2b.com/index.php?route=account/sales_order/sales_order_management',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'x-gmd-page-url': 'https://www.gigab2b.com/index.php?route=account/sales_order/sales_order_management',
    'x-gmd-referer-url': 'https://www.gigab2b.com/index.php?route=common/home',
    'x-gmd-screen': '2560x1440',
    'x-requested-with': 'XMLHttpRequest, XMLHttpRequest',
    # 'cookie': 'country=USA; currency=USD; gmd_device_id=0fbf4233-891d-4d96-aac6-2e54eed0e298; _ga=GA1.1.*********.**********; _gcl_au=1.1.**********.**********; acw_tc=ac11000117514696863582946e006795611e32620996a077bf2ee456bd2acf; acw_sc__v2=68654e76cb74f3253c4353101b7066d8669d66bc; _ayo=bdf58dfce3a6121c012e2fdda6d52845; OCSESSID=a40aa1fb3d82d1dfcfc38b7510; tfstk=ggEqpn60hiIVvylxouiwa0E8PCoxfc5C3lGsIR2ihjcDcnOgzWV4GtQtSVzzs73jirwsQhyxNfNsflzJbJeTctn1HmexXcfCOMiaH-nObcWf7oJoqvekjhYcP0bfn05ROMsQnJHCf-5Qftn70jDijx0ijYXrCvoinxViZ4DndImgs5coEAk9oCcDj4DowAmis5mGU8c-QcciplVgM3lroOB9GjrWm0gqt-cyY8ErnJLY3b-HxuzruXmZahxg4xyabxr2YNG3JjgIz7jW0cyoQSG7tMxrq2zLCAPV0nmbrzExcu1epqPZG2q-zgArUuyQ-YeP8Tyzbju3m4J6AmyrI50ay6-I3mar8ozABQ20OjznDR9FG8uUz2Hord8mcywQX2qc0BhSJAPjNWjH400G4GAtED47XrRM7qD-UX6PUX8ZS-p101akWF3PyYlCp9L9WqDEUX6PpFLtrcDrOttf.; login_flag=1; is_partner=0; _ga_39N4BF4XSG=GS2.1.s**********$o1$g1$t1751470624$j51$l0$h0',
}

response = requests.get(
    'https://www.gigab2b.com/index.php?route=/account/sales_order/sales_order_management/getSalesOrderListApi&filter_item_code=&filter_orderId=&filter_orderDate_from=2025-01-02+00:00:00&filter_orderDate_to=2025-07-02+23:59:59&page=1&page_limit=100',
    cookies=cookies,
    headers=headers,
)
print(response.text)
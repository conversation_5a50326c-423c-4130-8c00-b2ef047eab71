#ifndef _UXTHEME_H
#define _UXTHEME_H
#if __GNUC__ >= 3
#pragma GCC system_header
#endif

#include <commctrl.h>

#ifdef __cplusplus
extern "C" {
#endif

#if (_WIN32_WINNT >= 0x0501)
#define DTBG_CLIPRECT 0x00000001
#define DTBG_DRAWSOLID 0x00000002
#define DTBG_OMITBORDER 0x00000004
#define DTBG_OMITCONTENT 0x00000008
#define DTBG_COMPUTINGREGION 0x00000010
#define DTBG_MIRRORDC 0x00000020
#define DTT_GRAYED 0x00000001
#define ETDT_DISABLE 0x00000001
#define ETDT_ENABLE 0x00000002
#define ETDT_USETABTEXTURE 0x00000004
#define ETDT_ENABLETAB (ETDT_ENABLE|ETDT_USETABTEXTURE)
#define STAP_ALLOW_NONCLIENT 0x00000001
#define STAP_ALLOW_CONTROLS 0x00000002
#define STAP_ALLOW_WEBCONTENT 0x00000004
#define HTTB_BACKGROUNDSEG 0x0000
#define HTTB_FIXEDBORDER 0x0002
#define HTTB_CAPTION 0x0004
#define HTTB_RESIZINGBORDER_LEFT 0x0010
#define HTTB_RESIZINGBORDER_TOP 0x0020
#define HTTB_RESIZINGBORDER_RIGHT 0x0040
#define HTTB_RESIZINGBORDER_BOTTOM 0x0080
#define HTTB_RESIZINGBORDER (HTTB_RESIZINGBORDER_LEFT|HTTB_RESIZINGBORDER_TOP|HTTB_RESIZINGBORDER_RIGHT|HTTB_RESIZINGBORDER_BOTTOM)
#define HTTB_SIZINGTEMPLATE 0x0100
#define HTTB_SYSTEMSIZINGMARGINS 0x0200
#define TMT_DISPLAYNAME 0x0259
#define TMT_TOOLTIP 0x025A
#define TMT_COMPANY 0x025B
#define TMT_AUTHOR 0x025C
#define TMT_COPYRIGHT 0x025D
#define TMT_URL 0x025E
#define TMT_VERSION 0x025F
#define TMT_DESCRIPTION 0x0260
#define TMT_CAPTIONFONT 0x0321
#define TMT_SMALLCAPTIONFONT 0x0322
#define TMT_MENUFONT 0x0323
#define TMT_STATUSFONT 0x0324
#define TMT_MSGBOXFONT 0x0325
#define TMT_ICONTITLEFONT 0x0326
#define TMT_FLATMENUS 0x03E9
#define TMT_MINCOLORDEPTH 0x0515
#define TMT_CSSNAME 0x0579
#define TMT_XMLNAME 0x057A
#define TMT_SCROLLBAR 0x0641
#define TMT_BACKGROUND 0x0642
#define TMT_ACTIVECAPTION 0x0643
#define TMT_INACTIVECAPTION 0x0644
#define TMT_WINDOW 0x0646
#define TMT_WINDOWFRAME 0x0647
#define TMT_MENUTEXT 0x0648
#define TMT_WINDOWTEXT 0x0649
#define TMT_CAPTIONTEXT 0x064A
#define TMT_ACTIVEBORDER 0x064B
#define TMT_INACTIVEBORDER 0x064C
#define TMT_APPWORKSPACE 0x064D
#define TMT_HIGHLIGHT 0x064E
#define TMT_HIGHLIGHTTEXT 0x064F
#define TMT_BTNFACE 0x0650
#define TMT_BTNSHADOW 0x0651
#define TMT_GRAYTEXT 0x0652
#define TMT_BTNTEXT 0x0653
#define TMT_INACTIVECAPTIONTEXT 0x0654
#define TMT_BTNHIGHLIGHT 0x0655
#define TMT_DKSHADOW3D 0x0656
#define TMT_LIGHT3D 0x0657
#define TMT_INFOTEXT 0x0658
#define TMT_INFOBK 0x0659
#define TMT_BUTTONALTERNATEFACE 0x065A
#define TMT_HOTTRACKING 0x065B
#define TMT_GRADIENTACTIVECAPTION 0x065C
#define TMT_GRADIENTINACTIVECAPTION 0x065D
#define TMT_MENUHILIGHT 0x065E
#define TMT_MENUBAR 0x065F
#define TMT_TRANSPARENT 0x0899
#define TMT_AUTOSIZE 0x089A
#define TMT_BORDERONLY 0x089B
#define TMT_COMPOSITED 0x089C
#define TMT_BGFILL 0x089D
#define TMT_GLYPHTRANSPARENT 0x089E
#define TMT_GLYPHONLY 0x089F
#define TMT_ALWAYSSHOWSIZINGBAR 0x08A0
#define TMT_MIRRORIMAGE 0x08A1
#define TMT_UNIFORMSIZING 0x08A2
#define TMT_INTEGRALSIZING 0x08A3
#define TMT_SOURCEGROW 0x08A4
#define TMT_SOURCESHRINK 0x08A5
#define TMT_IMAGECOUNT 0x0961
#define TMT_ALPHALEVEL 0x0962
#define TMT_BORDERSIZE 0x0963
#define TMT_ROUNDCORNERWIDTH 0x0964
#define TMT_ROUNDCORNERHEIGHT 0x0965
#define TMT_GRADIENTRATIO1 0x0966
#define TMT_GRADIENTRATIO2 0x0967
#define TMT_GRADIENTRATIO3 0x0968
#define TMT_GRADIENTRATIO4 0x0969
#define TMT_GRADIENTRATIO5 0x096A
#define TMT_PROGRESSCHUNKSIZE 0x096B
#define TMT_PROGRESSSPACESIZE 0x096C
#define TMT_SATURATION 0x096D
#define TMT_TEXTBORDERSIZE 0x096E
#define TMT_ALPHATHRESHOLD 0x096F
#define TMT_WIDTH 0x0970
#define TMT_HEIGHT 0x0971
#define TMT_GLYPHINDEX 0x0972
#define TMT_TRUESIZESTRETCHMARK 0x0973
#define TMT_MINDPI1 0x0974
#define TMT_MINDPI2 0x0975
#define TMT_MINDPI3 0x0976
#define TMT_MINDPI4 0x0977
#define TMT_MINDPI5 0x0978
#define TMT_GLYPHFONT 0x0A29
#define TMT_IMAGEFILE 0x0BB9
#define TMT_IMAGEFILE1 0x0BBA
#define TMT_IMAGEFILE2 0x0BBB
#define TMT_IMAGEFILE3 0x0BBC
#define TMT_IMAGEFILE4 0x0BBD
#define TMT_IMAGEFILE5 0x0BBE
#define TMT_STOCKIMAGEFILE 0x0BBF
#define TMT_GLYPHIMAGEFILE 0x0BC0
#define TMT_TEXT 0x0C81
#define TMT_OFFSET 0x0D49
#define TMT_TEXTSHADOWOFFSET 0x0D4A
#define TMT_MINSIZE 0x0D4B
#define TMT_MINSIZE1 0x0D4C
#define TMT_MINSIZE2 0x0D4D
#define TMT_MINSIZE3 0x0D4E
#define TMT_MINSIZE4 0x0D4F
#define TMT_MINSIZE5 0x0D50
#define TMT_NORMALSIZE 0x0D51
#define TMT_SIZINGMARGINS 0x0E11
#define TMT_CONTENTMARGINS 0x0E12
#define TMT_CAPTIONMARGINS 0x0E13
#define TMT_BORDERCOLOR 0x0ED9
#define TMT_FILLCOLOR 0x0EDA
#define TMT_TEXTCOLOR 0x0EDB
#define TMT_EDGELIGHTCOLOR 0x0EDC
#define TMT_EDGEHIGHLIGHTCOLOR 0x0EDD
#define TMT_EDGESHADOWCOLOR 0x0EDE
#define TMT_EDGEDKSHADOWCOLOR 0x0EDF
#define TMT_EDGEFILLCOLOR 0x0EE0
#define TMT_TRANSPARENTCOLOR 0x0EE1
#define TMT_GRADIENTCOLOR1 0x0EE2
#define TMT_GRADIENTCOLOR2 0x0EE3
#define TMT_GRADIENTCOLOR3 0x0EE4
#define TMT_GRADIENTCOLOR4 0x0EE5
#define TMT_GRADIENTCOLOR5 0x0EE6
#define TMT_SHADOWCOLOR 0x0EE7
#define TMT_GLOWCOLOR 0x0EE8
#define TMT_TEXTBORDERCOLOR 0x0EE9
#define TMT_TEXTSHADOWCOLOR 0x0EEA
#define TMT_GLYPHTEXTCOLOR 0x0EEB
#define TMT_GLYPHTRANSPARENTCOLOR 0x0EEC
#define TMT_FILLCOLORHINT 0x0EED
#define TMT_BORDERCOLORHINT 0x0EEE
#define TMT_ACCENTCOLORHINT 0x0EEF
#define TMT_BGTYPE 0x0FA1
#define TMT_BORDERTYPE 0x0FA2
#define TMT_FILLTYPE 0x0FA3
#define TMT_SIZINGTYPE 0x0FA4
#define TMT_HALIGN 0x0FA5
#define TMT_CONTENTALIGNMENT 0x0FA6
#define TMT_VALIGN 0x0FA7
#define TMT_OFFSETTYPE 0x0FA8
#define TMT_ICONEFFECT 0x0FA9
#define TMT_TEXTSHADOWTYPE 0x0FAA
#define TMT_IMAGELAYOUT 0x0FAB
#define TMT_GLYPHTYPE 0x0FAC
#define TMT_IMAGESELECTTYPE 0x0FAD
#define TMT_GLYPHFONTSIZINGTYPE 0x0FAE
#define TMT_TRUESIZESCALINGTYPE 0x0FAF
#define TMT_USERPICTURE 0x1389
#define TMT_DEFAULTPANESIZE 0x138A
#define TMT_BLENDCOLOR 0x138B
#define BT_IMAGEFILE  0x0000
#define BT_BORDERFILL 0x0001
#define BT_NONE       0x0002

typedef enum PROPERTYORIGIN {
	PO_STATE = 0,
	PO_PART = 1,
	PO_CLASS = 2,
	PO_GLOBAL = 3,
	PO_NOTFOUND = 4
} PROPERTYORIGIN;
typedef enum THEMESIZE {
	TS_MIN,
	TS_TRUE,
	TS_DRAW
} THEME_SIZE;
typedef struct _DTBGOPTS {
	DWORD dwSize;
	DWORD dwFlags;
	RECT rcClip;
} DTBGOPTS, *PDTBGOPTS;
#define MAX_INTLIST_COUNT 10
typedef struct _INTLIST {
	int iValueCount;
	int iValues[MAX_INTLIST_COUNT];
} INTLIST, *PINTLIST;
typedef struct _MARGINS {
	int cxLeftWidth;
	int cxRightWidth;
	int cyTopHeight;
	int cyBottomHeight;
} MARGINS, *PMARGINS;
typedef HANDLE HTHEME;

HRESULT WINAPI CloseThemeData(HTHEME);
HRESULT WINAPI DrawThemeBackground(HTHEME,HDC,int,int,const RECT*,const RECT*);
HRESULT WINAPI DrawThemeBackgroundEx(HTHEME,HDC,int,int,const RECT*,const DTBGOPTS*);
HRESULT WINAPI DrawThemeEdge(HTHEME,HDC,int,int,const RECT*,UINT,UINT,RECT*);
HRESULT WINAPI DrawThemeIcon(HTHEME,HDC,int,int,const RECT*,HIMAGELIST,int);
HRESULT WINAPI DrawThemeParentBackground(HWND,HDC,RECT*);
HRESULT WINAPI DrawThemeText(HTHEME,HDC,int,int,LPCWSTR,int,DWORD,DWORD,const RECT*);
HRESULT WINAPI EnableThemeDialogTexture(HWND,DWORD);
HRESULT WINAPI EnableTheming(BOOL);
HRESULT WINAPI GetCurrentThemeName(LPWSTR,int,LPWSTR,int,LPWSTR,int);
DWORD WINAPI GetThemeAppProperties();
HRESULT WINAPI GetThemeBackgroundContentRect(HTHEME,HDC,int,int,const RECT*,RECT*);
HRESULT WINAPI GetThemeBackgroundExtent(HTHEME,HDC,int,int,const RECT*,RECT*);
HRESULT WINAPI GetThemeBackgroundRegion(HTHEME,HDC,int,int,const RECT*,HRGN*);
HRESULT WINAPI GetThemeBool(HTHEME,int,int,int,BOOL*);
HRESULT WINAPI GetThemeColor(HTHEME,int,int,int,COLORREF*);
HRESULT WINAPI GetThemeDocumentationProperty(LPCWSTR,LPCWSTR,LPWSTR,int);
HRESULT WINAPI GetThemeEnumValue(HTHEME,int,int,int,int*);
HRESULT WINAPI GetThemeFilename(HTHEME,int,int,int,LPWSTR,int);
HRESULT WINAPI GetThemeFont(HTHEME,HDC,int,int,int,LOGFONT*);
HRESULT WINAPI GetThemeInt(HTHEME,int,int,int,int*);
HRESULT WINAPI GetThemeIntList(HTHEME,int,int,int,INTLIST*);
HRESULT WINAPI GetThemeMargins(HTHEME,HDC,int,int,int,RECT*,MARGINS*);
HRESULT WINAPI GetThemeMetric(HTHEME,HDC,int,int,int,int*);
HRESULT WINAPI GetThemePartSize(HTHEME,HDC,int,int,RECT*,THEME_SIZE,SIZE*);
HRESULT WINAPI GetThemePosition(HTHEME,int,int,int,POINT*);
HRESULT WINAPI GetThemePropertyOrigin(HTHEME,int,int,int,PROPERTYORIGIN*);
HRESULT WINAPI GetThemeRect(HTHEME,int,int,int,RECT*);
HRESULT WINAPI GetThemeString(HTHEME,int,int,int,LPWSTR,int);
BOOL WINAPI GetThemeSysBool(HTHEME,int);
COLORREF WINAPI GetThemeSysColor(HTHEME,int);
HBRUSH WINAPI GetThemeSysColorBrush(HTHEME,int);
HRESULT WINAPI GetThemeSysFont(HTHEME,int,LOGFONT*);
HRESULT WINAPI GetThemeSysInt(HTHEME,int,int*);
int WINAPI GetThemeSysSize(HTHEME,int);
HRESULT WINAPI GetThemeSysString(HTHEME,int,LPWSTR,int);
HRESULT WINAPI GetThemeTextExtent(HTHEME,HDC,int,int,LPCWSTR,int,DWORD,const RECT*,RECT*);
HRESULT WINAPI GetThemeTextMetrics(HTHEME,HDC,int,int,TEXTMETRIC*);
HTHEME WINAPI GetWindowTheme(HWND);
HRESULT WINAPI HitTestThemeBackground(HTHEME,HDC,int,int,DWORD,const RECT*,HRGN,POINT,WORD*);
BOOL WINAPI IsAppThemed();
BOOL WINAPI IsThemeActive();
BOOL WINAPI IsThemeBackgroundPartiallyTransparent(HTHEME,int,int);
BOOL WINAPI IsThemeDialogTextureEnabled(HWND);
BOOL WINAPI IsThemePartDefined(HTHEME,int,int);
HTHEME WINAPI OpenThemeData(HWND,LPCWSTR);
void WINAPI SetThemeAppProperties(DWORD);
HRESULT WINAPI SetWindowTheme(HWND,LPCWSTR,LPCWSTR);
#endif

#ifdef __cplusplus
}
#endif
#endif

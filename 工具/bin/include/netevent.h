/*
 * netevent.h
 *
 * Network events
 *
 * This file is part of the w32api package.
 *
 * Contributors:
 *   Created by <PERSON> <<EMAIL>>
 *
 * THIS SOFTWARE IS NOT COPYRIGHTED
 *
 * This source code is offered for use in the public domain. You may
 * use, modify or distribute it freely.
 *
 * This code is distributed in the hope that it will be useful but
 * WITHOUT ANY WARRANTY. ALL WARRANTIES, EXPRESS OR IMPLIED ARE HEREBY
 * DISCLAIMED. This includes but is not limited to warranties of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *
 */

#ifndef __NETEVENT_H
#define __NETEVENT_H

#if __GNUC__ >=3
#pragma GCC system_header
#endif

#define EVENT_TRANSPORT_REGISTER_FAILED  0xC000232CL

#define EVENT_TRANSPORT_ADAPTER_NOT_FOUND 0xC000232EL

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

#endif /* __NETEVENT_H */

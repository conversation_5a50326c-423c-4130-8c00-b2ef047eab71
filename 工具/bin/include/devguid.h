#ifndef _DEVGUID_H
#define _DEVGUID_H
#if __GNUC__ >=3
#pragma GCC system_header
#endif

#ifdef __cplusplus
extern "C" {
#endif

extern const GUID GUID_DEVCLASS_WCEUSBS;
extern const GUID GUID_DEVCLASS_USB;
extern const GUID GUID_DEVCLASS_PNPPRINTERS;
extern const GUID GUID_DEVCLASS_DOT4;
extern const GUID GUID_DEVCLASS_DOT4PRINT;
extern const GUID GUID_DEVCLASS_CDROM;
extern const GUID GUID_DEVCLASS_COMPUTER;
extern const GUID GUID_DEVCLASS_DISKDRIVE;
extern const GUID GUID_DEVCLASS_DISPLAY;
extern const GUID GUID_DEVCLASS_FDC;
extern const GUID GUID_DEVCLASS_HDC;
extern const GUID GUID_DEVCLASS_KEYBOARD;
extern const GUID GUID_DEVCLASS_MEDIA;
extern const GUID GUID_DEVCLASS_MODEM;
extern const GUID GUID_DEVCLASS_MONITOR;
extern const GUID GUID_DEVCLASS_MOUSE;
extern const GUID GUID_DEVCLASS_MTD;
extern const GUID GUID_DEVCLASS_MULTIFUNCTION;
extern const GUID GUID_DEVCLASS_NET;
extern const GUID GUID_DEVCLASS_NETCLIENT;
extern const GUID GUID_DEVCLASS_NETSERVICE;
extern const GUID GUID_DEVCLASS_NETTRANS;
extern const GUID GUID_DEVCLASS_PCMCIA;
extern const GUID GUID_DEVCLASS_PORTS;
extern const GUID GUID_DEVCLASS_PRINTER;
extern const GUID GUID_DEVCLASS_SCSIADAPTER;
extern const GUID GUID_DEVCLASS_SYSTEM;
extern const GUID GUID_DEVCLASS_UNKNOWN;
extern const GUID GUID_DEVCLASS_FLOPPYDISK;
extern const GUID GUID_DEVCLASS_PROCESSOR;
extern const GUID GUID_DEVCLASS_MULTIPORTSERIAL;
extern const GUID GUID_DEVCLASS_SMARTCARDREADER;
extern const GUID GUID_DEVCLASS_VOLUMESNAPSHOT;
extern const GUID GUID_DEVCLASS_1394DEBUG;
extern const GUID GUID_DEVCLASS_1394;
extern const GUID GUID_DEVCLASS_INFRARED;
extern const GUID GUID_DEVCLASS_IMAGE;
extern const GUID GUID_DEVCLASS_TAPEDRIVE;
extern const GUID GUID_DEVCLASS_VOLUME;
extern const GUID GUID_DEVCLASS_BATTERY;
extern const GUID GUID_DEVCLASS_HIDCLASS;
extern const GUID GUID_DEVCLASS_61883;
extern const GUID GUID_DEVCLASS_LEGACYDRIVER;
extern const GUID GUID_DEVCLASS_SDHOST;
extern const GUID GUID_DEVCLASS_AVC;
extern const GUID GUID_DEVCLASS_ENUM1394;
extern const GUID GUID_DEVCLASS_MEDIUMCHANGER;
extern const GUID GUID_DEVCLASS_NTAPM;
extern const GUID GUID_DEVCLASS_SBP2;
extern const GUID GUID_DEVCLASS_BLUETOOTH;
extern const GUID GUID_DEVCLASS_PROBES;
#ifdef __cplusplus
}
#endif
#endif /* _DEVGUID_H */

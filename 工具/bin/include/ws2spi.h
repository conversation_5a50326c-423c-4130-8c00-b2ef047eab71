/*
 * ws2spi.h
 *
 * WinSock v2 Service Provider Interface.
 *
 * $Id: ws2spi.h,v 45c0695752ea 2017/11/24 17:04:18 keith $
 *
 * Written by <PERSON> <<EMAIL>>
 * Copyright (C) 2002-2004, 2006, 2017, MinGW.org Project
 *
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice (including the next
 * paragraph) shall be included in all copies or substantial portions of the
 * Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 *
 */
#ifndef _WS2SPI_H
#pragma GCC system_header
#define _WS2SPI_H

#include <winsock2.h>

_BEGIN_C_DECLS

#define WSPAPI			WSAAPI
#define WSPDESCRIPTION_LEN	  255

#ifndef RC_INVOKED

typedef
struct WSPData
{ WORD		wVersion;
  WORD		wHighVersion;
  WCHAR 	szDescription[WSPDESCRIPTION_LEN+1];
} WSPDATA, FAR *LPWSPDATA;

typedef
struct _WSATHREADID
{ HANDLE	ThreadHandle;
  DWORD 	Reserved;
} WSATHREADID, FAR *LPWSATHREADID;

typedef BOOL (CALLBACK FAR *LPBLOCKINGCALLBACK) (DWORD);
typedef VOID (CALLBACK FAR *LPWSAUSERAPC) (DWORD);

/* Prototypes for functions which may be invoked
 * via the Service Provider Procedure Table.
 */
typedef SOCKET (WSPAPI *LPWSPACCEPT)
  ( SOCKET, LPSOCKADDR, LPINT, LPCONDITIONPROC, DWORD, LPINT
  );
typedef INT (WSPAPI *LPWSPADDRESSTOSTRING)
  ( LPSOCKADDR, DWORD, LPWSAPROTOCOL_INFOW, LPWSTR, LPDWORD, LPINT
  );
typedef INT (WSPAPI *LPWSPASYNCSELECT) (SOCKET, HWND, UINT, LONG, LPINT);
typedef INT (WSPAPI *LPWSPBIND) (SOCKET, const struct sockaddr *, INT, LPINT);
typedef INT (WSPAPI *LPWSPCANCELBLOCKINGCALL) (LPINT);
typedef INT (WSPAPI *LPWSPCLEANUP) (LPINT);
typedef INT (WSPAPI *LPWSPCLOSESOCKET) (SOCKET, LPINT);

typedef INT (WSPAPI *LPWSPCONNECT)
  ( SOCKET, const struct sockaddr *, INT, LPWSABUF, LPWSABUF,
    LPQOS, LPQOS, LPINT
  );
typedef INT (WSPAPI *LPWSPDUPLICATESOCKET)
  ( SOCKET, DWORD, LPWSAPROTOCOL_INFOW, LPINT
  );
typedef INT (WSPAPI *LPWSPENUMNETWORKEVENTS)
  ( SOCKET, WSAEVENT, LPWSANETWORKEVENTS, LPINT
  );
typedef INT (WSPAPI *LPWSPEVENTSELECT) (SOCKET, WSAEVENT, LONG, LPINT);

typedef BOOL (WSPAPI *LPWSPGETOVERLAPPEDRESULT)
  ( SOCKET, LPWSAOVERLAPPED, LPDWORD, BOOL, LPDWORD, LPINT
  );
typedef INT (WSPAPI *LPWSPGETPEERNAME) (SOCKET, LPSOCKADDR, LPINT, LPINT);
typedef BOOL (WSPAPI *LPWSPGETQOSBYNAME) (SOCKET, LPWSABUF, LPQOS, LPINT);
typedef INT (WSPAPI *LPWSPGETSOCKNAME) (SOCKET, LPSOCKADDR, LPINT, LPINT);

typedef INT (WSPAPI *LPWSPGETSOCKOPT)
  ( SOCKET, INT, INT, CHAR FAR *, LPINT, LPINT
  );
typedef INT (WSPAPI *LPWSPIOCTL)
  ( SOCKET, DWORD, LPVOID, DWORD, LPVOID, DWORD, LPDWORD, LPWSAOVERLAPPED,
    LPWSAOVERLAPPED_COMPLETION_ROUTINE, LPWSATHREADID, LPINT
  );
typedef SOCKET (WSPAPI *LPWSPJOINLEAF)
  ( SOCKET, const struct sockaddr*, INT, LPWSABUF, LPWSABUF,
    LPQOS, LPQOS, DWORD, LPINT
  );
typedef INT (WSPAPI *LPWSPLISTEN) (SOCKET, INT, LPINT);

typedef INT (WSPAPI *LPWSPRECV)
  ( SOCKET, LPWSABUF, DWORD, LPDWORD, LPDWORD, LPWSAOVERLAPPED,
    LPWSAOVERLAPPED_COMPLETION_ROUTINE, LPWSATHREADID, LPINT
  );
typedef INT (WSPAPI *LPWSPRECVDISCONNECT) (SOCKET, LPWSABUF, LPINT);

typedef INT (WSPAPI *LPWSPRECVFROM)
  ( SOCKET, LPWSABUF, DWORD, LPDWORD, LPDWORD, LPSOCKADDR, LPINT,
    LPWSAOVERLAPPED, LPWSAOVERLAPPED_COMPLETION_ROUTINE, LPWSATHREADID,
    LPINT
  );
typedef INT (WSPAPI *LPWSPSELECT)
  ( INT, fd_set *, fd_set *, fd_set *, CONST TIMEVAL *, LPINT
  );
typedef INT (WSPAPI *LPWSPSEND)
  ( SOCKET, LPWSABUF, DWORD, LPDWORD, DWORD, LPWSAOVERLAPPED,
    LPWSAOVERLAPPED_COMPLETION_ROUTINE, LPWSATHREADID, LPINT
  );
typedef INT (WSPAPI *LPWSPSENDDISCONNECT) (SOCKET, LPWSABUF, LPINT);

typedef INT (WSPAPI *LPWSPSENDTO)
  ( SOCKET, LPWSABUF, DWORD, LPDWORD, DWORD, const struct sockaddr *,
    INT, LPWSAOVERLAPPED, LPWSAOVERLAPPED_COMPLETION_ROUTINE,
    LPWSATHREADID, LPINT
  );
typedef INT (WSPAPI *LPWSPSETSOCKOPT)
  ( SOCKET, INT, INT, CONST CHAR FAR *, INT, LPINT
  );
typedef INT (WSPAPI *LPWSPSHUTDOWN) (SOCKET, INT, LPINT);

typedef SOCKET (WSPAPI *LPWSPSOCKET)
  ( INT, INT, INT, LPWSAPROTOCOL_INFOW, GROUP, DWORD, LPINT
  );
typedef INT (WSPAPI *LPWSPSTRINGTOADDRESS)
  ( LPWSTR, INT, LPWSAPROTOCOL_INFOW, LPSOCKADDR, LPINT, LPINT
  );

typedef
struct _WSPPROC_TABLE
{ /* Service Provider Procedure Table
   */
  LPWSPACCEPT			lpWSPAccept;
  LPWSPADDRESSTOSTRING		lpWSPAddressToString;
  LPWSPASYNCSELECT		lpWSPAsyncSelect;
  LPWSPBIND			lpWSPBind;
  LPWSPCANCELBLOCKINGCALL	lpWSPCancelBlockingCall;
  LPWSPCLEANUP			lpWSPCleanup;
  LPWSPCLOSESOCKET		lpWSPCloseSocket;
  LPWSPCONNECT			lpWSPConnect;
  LPWSPDUPLICATESOCKET		lpWSPDuplicateSocket;
  LPWSPENUMNETWORKEVENTS	lpWSPEnumNetworkEvents;
  LPWSPEVENTSELECT		lpWSPEventSelect;
  LPWSPGETOVERLAPPEDRESULT	lpWSPGetOverlappedResult;
  LPWSPGETPEERNAME		lpWSPGetPeerName;
  LPWSPGETSOCKNAME		lpWSPGetSockName;
  LPWSPGETSOCKOPT		lpWSPGetSockOpt;
  LPWSPGETQOSBYNAME		lpWSPGetQOSByName;
  LPWSPIOCTL			lpWSPIoctl;
  LPWSPJOINLEAF 		lpWSPJoinLeaf;
  LPWSPLISTEN			lpWSPListen;
  LPWSPRECV			lpWSPRecv;
  LPWSPRECVDISCONNECT		lpWSPRecvDisconnect;
  LPWSPRECVFROM 		lpWSPRecvFrom;
  LPWSPSELECT			lpWSPSelect;
  LPWSPSEND			lpWSPSend;
  LPWSPSENDDISCONNECT		lpWSPSendDisconnect;
  LPWSPSENDTO			lpWSPSendTo;
  LPWSPSETSOCKOPT		lpWSPSetSockOpt;
  LPWSPSHUTDOWN 		lpWSPShutdown;
  LPWSPSOCKET			lpWSPSocket;
  LPWSPSTRINGTOADDRESS		lpWSPStringToAddress;
} WSPPROC_TABLE, FAR *LPWSPPROC_TABLE;

/* Prototypes for functions which may be invoked via
 * the Service Provider Upcall Procedure Table.
 */
typedef BOOL (WSPAPI *LPWPUCLOSEEVENT) (WSAEVENT, LPINT);
typedef INT (WSPAPI *LPWPUCLOSESOCKETHANDLE) (SOCKET, LPINT);
typedef INT (WSPAPI *LPWPUCLOSETHREAD) (LPWSATHREADID, LPINT);
typedef WSAEVENT (WSPAPI *LPWPUCREATEEVENT) (LPINT);
typedef SOCKET (WSPAPI *LPWPUCREATESOCKETHANDLE) (DWORD, DWORD, LPINT);
typedef SOCKET (WSPAPI *LPWPUFDISSET) (SOCKET, fd_set *);
typedef INT (WSPAPI *LPWPUGETPROVIDERPATH) (LPGUID, LPWSTR, LPINT, LPINT);
typedef SOCKET (WSPAPI *LPWPUMODIFYIFSHANDLE) (DWORD, SOCKET, LPINT);
typedef INT (WSPAPI *LPWPUOPENCURRENTTHREAD) (LPWSATHREADID, LPINT);
typedef BOOL (WSPAPI *LPWPUPOSTMESSAGE) (HWND, UINT, WPARAM, LPARAM);

typedef INT (WSPAPI *LPWPUQUERYBLOCKINGCALLBACK)
  ( DWORD, LPBLOCKINGCALLBACK FAR *, LPDWORD, LPINT
  );
typedef INT (WSPAPI *LPWPUQUERYSOCKETHANDLECONTEXT) (SOCKET, LPDWORD, LPINT);

typedef INT (WSPAPI *LPWPUQUEUEAPC)
  ( LPWSATHREADID, LPWSAUSERAPC, DWORD, LPINT
  );
typedef BOOL (WSPAPI *LPWPURESETEVENT) (WSAEVENT, LPINT);
typedef BOOL (WSPAPI *LPWPUSETEVENT) (WSAEVENT, LPINT);

/* Functions matching the following prototype are available
 * only directly from the DLL
 */
typedef INT (WSPAPI *LPWPUCOMPLETEOVERLAPPEDREQUEST)
  ( SOCKET, LPWSAOVERLAPPED, DWORD, DWORD, LPINT
  );

typedef
struct _WSPUPCALLTABLE
{ /* Service Provider Upcall Table
   */
  LPWPUCLOSEEVENT			lpWPUCloseEvent;
  LPWPUCLOSESOCKETHANDLE		lpWPUCloseSocketHandle;
  LPWPUCREATEEVENT			lpWPUCreateEvent;
  LPWPUCREATESOCKETHANDLE		lpWPUCreateSocketHandle;
  LPWPUFDISSET				lpWPUFDIsSet;
  LPWPUGETPROVIDERPATH			lpWPUGetProviderPath;
  LPWPUMODIFYIFSHANDLE			lpWPUModifyIFSHandle;
  LPWPUPOSTMESSAGE			lpWPUPostMessage;
  LPWPUQUERYBLOCKINGCALLBACK		lpWPUQueryBlockingCallback;
  LPWPUQUERYSOCKETHANDLECONTEXT 	lpWPUQuerySocketHandleContext;
  LPWPUQUEUEAPC				lpWPUQueueApc;
  LPWPURESETEVENT			lpWPUResetEvent;
  LPWPUSETEVENT				lpWPUSetEvent;
  LPWPUOPENCURRENTTHREAD		lpWPUOpenCurrentThread;
  LPWPUCLOSETHREAD			lpWPUCloseThread;
} WSPUPCALLTABLE, FAR *LPWSPUPCALLTABLE;

typedef INT (WSPAPI *LPWSPSTARTUP)
  ( WORD, LPWSPDATA, LPWSAPROTOCOL_INFOW, WSPUPCALLTABLE, LPWSPPROC_TABLE
  );

/* Prototypes for functions which may be invoked via
 * the Service Provider Namespace Procedure Table.
 */
typedef INT (WSPAPI *LPNSPCLEANUP) (LPGUID);

typedef INT (WSPAPI *LPNSPGETSERVICECLASSINFO)
  ( LPGUID, LPDWORD, LPWSASERVICECLASSINFOW
  );
typedef INT (WSPAPI *LPNSPINSTALLSERVICECLASS)
  ( LPGUID, LPWSASERVICECLASSINFOW
  );
typedef INT (WSPAPI *LPNSPLOOKUPSERVICEBEGIN)
  ( LPGUID, LPWSAQUERYSETW, LPWSASERVICECLASSINFOW, DWORD, LPHANDLE
  );
typedef INT (WSPAPI *LPNSPLOOKUPSERVICEEND) (HANDLE);
typedef INT (WSPAPI *LPNSPLOOKUPSERVICENEXT)
  ( HANDLE, DWORD, LPDWORD, LPWSAQUERYSET
  );
typedef INT (WSPAPI *LPNSPREMOVESERVICECLASS) (LPGUID, LPGUID);

typedef INT (WSPAPI *LPNSPSETSERVICE)
  ( LPGUID, LPWSASERVICECLASSINFOW, LPWSAQUERYSETW, WSAESETSERVICEOP, DWORD
  );

typedef
struct _NSP_ROUTINE
{ /* Service Provider Namespace Procedure Table
   */
  DWORD 			cbSize;
  DWORD 			dwMajorVersion;
  DWORD 			dwMinorVersion;
  LPNSPCLEANUP			NSPCleanup;
  LPNSPLOOKUPSERVICEBEGIN	NSPLookupServiceBegin;
  LPNSPLOOKUPSERVICENEXT	NSPLookupServiceNext;
  LPNSPLOOKUPSERVICEEND 	NSPLookupServiceEnd;
  LPNSPSETSERVICE		NSPSetService;
  LPNSPINSTALLSERVICECLASS	NSPInstallServiceClass;
  LPNSPREMOVESERVICECLASS	NSPRemoveServiceClass;
  LPNSPGETSERVICECLASSINFO	NSPGetServiceClassInfo;
} NSP_ROUTINE, *PNSP_ROUTINE, *LPNSP_ROUTINE;

INT WSPAPI NSPStartup (LPGUID, LPNSP_ROUTINE);

/* WinSock v2 DLL function prototypes
 */
INT WSPAPI WPUCompleteOverlappedRequest
  ( SOCKET, LPWSAOVERLAPPED, DWORD, DWORD, LPINT
  );
INT WSPAPI WSPStartup
  ( WORD, LPWSPDATA, LPWSAPROTOCOL_INFOW, WSPUPCALLTABLE, LPWSPPROC_TABLE
  );
INT WSPAPI WSCDeinstallProvider (LPGUID, LPINT);
INT WSPAPI WSCEnumProtocols (LPINT, LPWSAPROTOCOL_INFOW, LPDWORD, LPINT);
INT WSPAPI WSCGetProviderPath (LPGUID, LPWSTR, LPINT, LPINT);

INT WSPAPI WSCInstallProvider
  ( CONST LPGUID, CONST LPWSTR, CONST LPWSAPROTOCOL_INFOW, DWORD, LPINT
  );
INT WSPAPI WSCEnableNSProvider (LPGUID, BOOL);
INT WSPAPI WSCInstallNameSpace (LPWSTR, LPWSTR, DWORD, DWORD, LPGUID);
INT WSPAPI WSCUnInstallNameSpace (LPGUID);
INT WSPAPI WSCWriteProviderOrder (LPDWORD, DWORD);

#endif /* ! RC_INVOKED */

_END_C_DECLS

#endif	/* !_WS2SPI_H: $RCSfile: ws2spi.h,v $: end of file */

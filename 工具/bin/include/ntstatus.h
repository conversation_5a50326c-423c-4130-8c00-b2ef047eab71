/*
 * ntstatus.h
 *
 * Windows NT status codes
 *
 * This file is part of the w32api package.
 *
 * Contributors:
 *   Created by <PERSON> <<EMAIL>>
 *
 * THIS SOFTWARE IS NOT COPYRIGHTED
 *
 * This source code is offered for use in the public domain. You may
 * use, modify or distribute it freely.
 *
 * This code is distributed in the hope that it will be useful but
 * WITHOUT ANY WARRANTY. ALL WARRANTIES, EXPRESS OR IMPLIED ARE HEREBY
 * DISCLAIMED. This includes but is not limited to warranties of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *
 */

#ifndef _NTSTATUS_H
#define _NTSTATUS_H

#if __GNUC__ >=3
#pragma GCC system_header
#endif

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(STATUS_SUCCESS)
#define STATUS_SUCCESS ((NTSTATUS)0x00000000L)
#endif /* !STATUS_SUCCESS */
#define FACILITY_DEBUGGER 0x1
#define FACILITY_RPC_RUNTIME 0x2
#define FACILITY_RPC_STUBS 0x3
#define FACILITY_IO_ERROR_CODE 0x4
#define FACILITY_TERMINAL_SERVER 0xA
#define FACILITY_USB_ERROR_CODE 0x10
#define FACILITY_HID_ERROR_CODE 0x11
#define FACILITY_FIREWIRE_ERROR_CODE 0x12
#define FACILITY_CLUSTER_ERROR_CODE 0x13
#define FACILITY_ACPI_ERROR_CODE 0x14
#define FACILITY_SXS_ERROR_CODE 0x15
#define STATUS_SEVERITY_SUCCESS 0x0
#define STATUS_SEVERITY_INFORMATIONAL 0x1
#define STATUS_SEVERITY_WARNING 0x2
#define STATUS_SEVERITY_ERROR 0x3
#define STATUS_WAIT_0 ((NTSTATUS)0x00000000L)
#define STATUS_WAIT_1 ((NTSTATUS)0x00000001L)
#define STATUS_WAIT_2 ((NTSTATUS)0x00000002L)
#define STATUS_WAIT_3 ((NTSTATUS)0x00000003L)
#define STATUS_WAIT_63 ((NTSTATUS)0x0000003FL)
#define STATUS_ABANDONED ((NTSTATUS)0x00000080L)
#define STATUS_ABANDONED_WAIT_0 ((NTSTATUS)0x00000080L)
#define STATUS_ABANDONED_WAIT_63 ((NTSTATUS)0x000000BFL)
#define STATUS_USER_APC ((NTSTATUS)0x000000C0L)
#define STATUS_KERNEL_APC ((NTSTATUS)0x00000100L)
#define STATUS_ALERTED ((NTSTATUS)0x00000101L)
#define STATUS_TIMEOUT ((NTSTATUS)0x00000102L)
#define STATUS_PENDING ((NTSTATUS)0x00000103L)
#define STATUS_REPARSE ((NTSTATUS)0x00000104L)
#define STATUS_MORE_ENTRIES ((NTSTATUS)0x00000105L)
#define STATUS_NOT_ALL_ASSIGNED ((NTSTATUS)0x00000106L)
#define STATUS_SOME_NOT_MAPPED ((NTSTATUS)0x00000107L)
#define STATUS_OPLOCK_BREAK_IN_PROGRESS ((NTSTATUS)0x00000108L)
#define STATUS_VOLUME_MOUNTED ((NTSTATUS)0x00000109L)
#define STATUS_RXACT_COMMITTED ((NTSTATUS)0x0000010AL)
#define STATUS_NOTIFY_CLEANUP ((NTSTATUS)0x0000010BL)
#define STATUS_NOTIFY_ENUM_DIR ((NTSTATUS)0x0000010CL)
#define STATUS_NO_QUOTAS_FOR_ACCOUNT ((NTSTATUS)0x0000010DL)
#define STATUS_PRIMARY_TRANSPORT_CONNECT_FAILED ((NTSTATUS)0x0000010EL)
#define STATUS_PAGE_FAULT_TRANSITION ((NTSTATUS)0x00000110L)
#define STATUS_PAGE_FAULT_DEMAND_ZERO ((NTSTATUS)0x00000111L)
#define STATUS_PAGE_FAULT_COPY_ON_WRITE ((NTSTATUS)0x00000112L)
#define STATUS_PAGE_FAULT_GUARD_PAGE ((NTSTATUS)0x00000113L)
#define STATUS_PAGE_FAULT_PAGING_FILE ((NTSTATUS)0x00000114L)
#define STATUS_CACHE_PAGE_LOCKED ((NTSTATUS)0x00000115L)
#define STATUS_CRASH_DUMP ((NTSTATUS)0x00000116L)
#define STATUS_BUFFER_ALL_ZEROS ((NTSTATUS)0x00000117L)
#define STATUS_REPARSE_OBJECT ((NTSTATUS)0x00000118L)
#define STATUS_RESOURCE_REQUIREMENTS_CHANGED ((NTSTATUS)0x00000119L)
#define STATUS_TRANSLATION_COMPLETE ((NTSTATUS)0x00000120L)
#define STATUS_DS_MEMBERSHIP_EVALUATED_LOCALLY ((NTSTATUS)0x00000121L)
#define STATUS_NOTHING_TO_TERMINATE ((NTSTATUS)0x00000122L)
#define STATUS_PROCESS_NOT_IN_JOB ((NTSTATUS)0x00000123L)
#define STATUS_PROCESS_IN_JOB ((NTSTATUS)0x00000124L)
#define STATUS_OBJECT_NAME_EXISTS ((NTSTATUS)0x40000000L)
#define STATUS_THREAD_WAS_SUSPENDED ((NTSTATUS)0x40000001L)
#define STATUS_WORKING_SET_LIMIT_RANGE ((NTSTATUS)0x40000002L)
#define STATUS_IMAGE_NOT_AT_BASE ((NTSTATUS)0x40000003L)
#define STATUS_RXACT_STATE_CREATED ((NTSTATUS)0x40000004L)
#define STATUS_SEGMENT_NOTIFICATION ((NTSTATUS)0x40000005L)
#define STATUS_LOCAL_USER_SESSION_KEY ((NTSTATUS)0x40000006L)
#define STATUS_BAD_CURRENT_DIRECTORY ((NTSTATUS)0x40000007L)
#define STATUS_SERIAL_MORE_WRITES ((NTSTATUS)0x40000008L)
#define STATUS_REGISTRY_RECOVERED ((NTSTATUS)0x40000009L)
#define STATUS_FT_READ_RECOVERY_FROM_BACKUP ((NTSTATUS)0x4000000AL)
#define STATUS_FT_WRITE_RECOVERY ((NTSTATUS)0x4000000BL)
#define STATUS_SERIAL_COUNTER_TIMEOUT ((NTSTATUS)0x4000000CL)
#define STATUS_NULL_LM_PASSWORD ((NTSTATUS)0x4000000DL)
#define STATUS_IMAGE_MACHINE_TYPE_MISMATCH ((NTSTATUS)0x4000000EL)
#define STATUS_RECEIVE_PARTIAL ((NTSTATUS)0x4000000FL)
#define STATUS_RECEIVE_EXPEDITED ((NTSTATUS)0x40000010L)
#define STATUS_RECEIVE_PARTIAL_EXPEDITED ((NTSTATUS)0x40000011L)
#define STATUS_EVENT_DONE ((NTSTATUS)0x40000012L)
#define STATUS_EVENT_PENDING ((NTSTATUS)0x40000013L)
#define STATUS_CHECKING_FILE_SYSTEM ((NTSTATUS)0x40000014L)
#define STATUS_FATAL_APP_EXIT ((NTSTATUS)0x40000015L)
#define STATUS_PREDEFINED_HANDLE ((NTSTATUS)0x40000016L)
#define STATUS_WAS_UNLOCKED ((NTSTATUS)0x40000017L)
#define STATUS_SERVICE_NOTIFICATION ((NTSTATUS)0x40000018L)
#define STATUS_WAS_LOCKED ((NTSTATUS)0x40000019L)
#define STATUS_LOG_HARD_ERROR ((NTSTATUS)0x4000001AL)
#define STATUS_ALREADY_WIN32 ((NTSTATUS)0x4000001BL)
#define STATUS_WX86_UNSIMULATE ((NTSTATUS)0x4000001CL)
#define STATUS_WX86_CONTINUE ((NTSTATUS)0x4000001DL)
#define STATUS_WX86_SINGLE_STEP ((NTSTATUS)0x4000001EL)
#define STATUS_WX86_BREAKPOINT ((NTSTATUS)0x4000001FL)
#define STATUS_WX86_EXCEPTION_CONTINUE ((NTSTATUS)0x40000020L)
#define STATUS_WX86_EXCEPTION_LASTCHANCE ((NTSTATUS)0x40000021L)
#define STATUS_WX86_EXCEPTION_CHAIN ((NTSTATUS)0x40000022L)
#define STATUS_IMAGE_MACHINE_TYPE_MISMATCH_EXE ((NTSTATUS)0x40000023L)
#define STATUS_NO_YIELD_PERFORMED ((NTSTATUS)0x40000024L)
#define STATUS_TIMER_RESUME_IGNORED ((NTSTATUS)0x40000025L)
#define STATUS_ARBITRATION_UNHANDLED ((NTSTATUS)0x40000026L)
#define STATUS_CARDBUS_NOT_SUPPORTED ((NTSTATUS)0x40000027L)
#define STATUS_WX86_CREATEWX86TIB ((NTSTATUS)0x40000028L)
#define STATUS_MP_PROCESSOR_MISMATCH ((NTSTATUS)0x40000029L)
#define STATUS_HIBERNATED ((NTSTATUS)0x4000002AL)
#define STATUS_RESUME_HIBERNATION ((NTSTATUS)0x4000002BL)
#define STATUS_GUARD_PAGE_VIOLATION ((NTSTATUS)0x80000001L)
#define STATUS_DATATYPE_MISALIGNMENT ((NTSTATUS)0x80000002L)
#define STATUS_BREAKPOINT ((NTSTATUS)0x80000003L)
#define STATUS_SINGLE_STEP ((NTSTATUS)0x80000004L)
#define STATUS_BUFFER_OVERFLOW ((NTSTATUS)0x80000005L)
#define STATUS_NO_MORE_FILES ((NTSTATUS)0x80000006L)
#define STATUS_WAKE_SYSTEM_DEBUGGER ((NTSTATUS)0x80000007L)
#define STATUS_HANDLES_CLOSED ((NTSTATUS)0x8000000AL)
#define STATUS_NO_INHERITANCE ((NTSTATUS)0x8000000BL)
#define STATUS_GUID_SUBSTITUTION_MADE ((NTSTATUS)0x8000000CL)
#define STATUS_PARTIAL_COPY ((NTSTATUS)0x8000000DL)
#define STATUS_DEVICE_PAPER_EMPTY ((NTSTATUS)0x8000000EL)
#define STATUS_DEVICE_POWERED_OFF ((NTSTATUS)0x8000000FL)
#define STATUS_DEVICE_OFF_LINE ((NTSTATUS)0x80000010L)
#define STATUS_DEVICE_BUSY ((NTSTATUS)0x80000011L)
#define STATUS_NO_MORE_EAS ((NTSTATUS)0x80000012L)
#define STATUS_INVALID_EA_NAME ((NTSTATUS)0x80000013L)
#define STATUS_EA_LIST_INCONSISTENT ((NTSTATUS)0x80000014L)
#define STATUS_INVALID_EA_FLAG ((NTSTATUS)0x80000015L)
#define STATUS_VERIFY_REQUIRED ((NTSTATUS)0x80000016L)
#define STATUS_EXTRANEOUS_INFORMATION ((NTSTATUS)0x80000017L)
#define STATUS_RXACT_COMMIT_NECESSARY ((NTSTATUS)0x80000018L)
#define STATUS_NO_MORE_ENTRIES ((NTSTATUS)0x8000001AL)
#define STATUS_FILEMARK_DETECTED ((NTSTATUS)0x8000001BL)
#define STATUS_MEDIA_CHANGED ((NTSTATUS)0x8000001CL)
#define STATUS_BUS_RESET ((NTSTATUS)0x8000001DL)
#define STATUS_END_OF_MEDIA ((NTSTATUS)0x8000001EL)
#define STATUS_BEGINNING_OF_MEDIA ((NTSTATUS)0x8000001FL)
#define STATUS_MEDIA_CHECK ((NTSTATUS)0x80000020L)
#define STATUS_SETMARK_DETECTED ((NTSTATUS)0x80000021L)
#define STATUS_NO_DATA_DETECTED ((NTSTATUS)0x80000022L)
#define STATUS_REDIRECTOR_HAS_OPEN_HANDLES ((NTSTATUS)0x80000023L)
#define STATUS_SERVER_HAS_OPEN_HANDLES ((NTSTATUS)0x80000024L)
#define STATUS_ALREADY_DISCONNECTED ((NTSTATUS)0x80000025L)
#define STATUS_LONGJUMP ((NTSTATUS)0x80000026L)
#define STATUS_CLEANER_CARTRIDGE_INSTALLED ((NTSTATUS)0x80000027L)
#define STATUS_PLUGPLAY_QUERY_VETOED ((NTSTATUS)0x80000028L)
#define STATUS_UNWIND_CONSOLIDATE ((NTSTATUS)0x80000029L)
#define STATUS_CLUSTER_NODE_ALREADY_UP ((NTSTATUS)0x80130001L)
#define STATUS_CLUSTER_NODE_ALREADY_DOWN ((NTSTATUS)0x80130002L)
#define STATUS_CLUSTER_NETWORK_ALREADY_ONLINE ((NTSTATUS)0x80130003L)
#define STATUS_CLUSTER_NETWORK_ALREADY_OFFLINE ((NTSTATUS)0x80130004L)
#define STATUS_CLUSTER_NODE_ALREADY_MEMBER ((NTSTATUS)0x80130005L)
#define STATUS_UNSUCCESSFUL ((NTSTATUS)0xC0000001L)
#define STATUS_NOT_IMPLEMENTED ((NTSTATUS)0xC0000002L)
#define STATUS_INVALID_INFO_CLASS ((NTSTATUS)0xC0000003L)
#define STATUS_INFO_LENGTH_MISMATCH ((NTSTATUS)0xC0000004L)
#define STATUS_ACCESS_VIOLATION ((NTSTATUS)0xC0000005L)
#define STATUS_IN_PAGE_ERROR ((NTSTATUS)0xC0000006L)
#define STATUS_PAGEFILE_QUOTA ((NTSTATUS)0xC0000007L)
#define STATUS_INVALID_HANDLE ((NTSTATUS)0xC0000008L)
#define STATUS_BAD_INITIAL_STACK ((NTSTATUS)0xC0000009L)
#define STATUS_BAD_INITIAL_PC ((NTSTATUS)0xC000000AL)
#define STATUS_INVALID_CID ((NTSTATUS)0xC000000BL)
#define STATUS_TIMER_NOT_CANCELED ((NTSTATUS)0xC000000CL)
#define STATUS_INVALID_PARAMETER ((NTSTATUS)0xC000000DL)
#define STATUS_NO_SUCH_DEVICE ((NTSTATUS)0xC000000EL)
#define STATUS_NO_SUCH_FILE ((NTSTATUS)0xC000000FL)
#define STATUS_INVALID_DEVICE_REQUEST ((NTSTATUS)0xC0000010L)
#define STATUS_END_OF_FILE ((NTSTATUS)0xC0000011L)
#define STATUS_WRONG_VOLUME ((NTSTATUS)0xC0000012L)
#define STATUS_NO_MEDIA_IN_DEVICE ((NTSTATUS)0xC0000013L)
#define STATUS_UNRECOGNIZED_MEDIA ((NTSTATUS)0xC0000014L)
#define STATUS_NONEXISTENT_SECTOR ((NTSTATUS)0xC0000015L)
#define STATUS_MORE_PROCESSING_REQUIRED ((NTSTATUS)0xC0000016L)
#define STATUS_NO_MEMORY ((NTSTATUS)0xC0000017L)
#define STATUS_CONFLICTING_ADDRESSES ((NTSTATUS)0xC0000018L)
#define STATUS_NOT_MAPPED_VIEW ((NTSTATUS)0xC0000019L)
#define STATUS_UNABLE_TO_FREE_VM ((NTSTATUS)0xC000001AL)
#define STATUS_UNABLE_TO_DELETE_SECTION ((NTSTATUS)0xC000001BL)
#define STATUS_INVALID_SYSTEM_SERVICE ((NTSTATUS)0xC000001CL)
#define STATUS_ILLEGAL_INSTRUCTION ((NTSTATUS)0xC000001DL)
#define STATUS_INVALID_LOCK_SEQUENCE ((NTSTATUS)0xC000001EL)
#define STATUS_INVALID_VIEW_SIZE ((NTSTATUS)0xC000001FL)
#define STATUS_INVALID_FILE_FOR_SECTION ((NTSTATUS)0xC0000020L)
#define STATUS_ALREADY_COMMITTED ((NTSTATUS)0xC0000021L)
#define STATUS_ACCESS_DENIED ((NTSTATUS)0xC0000022L)
#define STATUS_BUFFER_TOO_SMALL ((NTSTATUS)0xC0000023L)
#define STATUS_OBJECT_TYPE_MISMATCH ((NTSTATUS)0xC0000024L)
#define STATUS_NONCONTINUABLE_EXCEPTION ((NTSTATUS)0xC0000025L)
#define STATUS_INVALID_DISPOSITION ((NTSTATUS)0xC0000026L)
#define STATUS_UNWIND ((NTSTATUS)0xC0000027L)
#define STATUS_BAD_STACK ((NTSTATUS)0xC0000028L)
#define STATUS_INVALID_UNWIND_TARGET ((NTSTATUS)0xC0000029L)
#define STATUS_NOT_LOCKED ((NTSTATUS)0xC000002AL)
#define STATUS_PARITY_ERROR ((NTSTATUS)0xC000002BL)
#define STATUS_UNABLE_TO_DECOMMIT_VM ((NTSTATUS)0xC000002CL)
#define STATUS_NOT_COMMITTED ((NTSTATUS)0xC000002DL)
#define STATUS_INVALID_PORT_ATTRIBUTES ((NTSTATUS)0xC000002EL)
#define STATUS_PORT_MESSAGE_TOO_LONG ((NTSTATUS)0xC000002FL)
#define STATUS_INVALID_PARAMETER_MIX ((NTSTATUS)0xC0000030L)
#define STATUS_INVALID_QUOTA_LOWER ((NTSTATUS)0xC0000031L)
#define STATUS_DISK_CORRUPT_ERROR ((NTSTATUS)0xC0000032L)
#define STATUS_OBJECT_NAME_INVALID ((NTSTATUS)0xC0000033L)
#define STATUS_OBJECT_NAME_NOT_FOUND ((NTSTATUS)0xC0000034L)
#define STATUS_OBJECT_NAME_COLLISION ((NTSTATUS)0xC0000035L)
#define STATUS_PORT_DISCONNECTED ((NTSTATUS)0xC0000037L)
#define STATUS_DEVICE_ALREADY_ATTACHED ((NTSTATUS)0xC0000038L)
#define STATUS_OBJECT_PATH_INVALID ((NTSTATUS)0xC0000039L)
#define STATUS_OBJECT_PATH_NOT_FOUND ((NTSTATUS)0xC000003AL)
#define STATUS_OBJECT_PATH_SYNTAX_BAD ((NTSTATUS)0xC000003BL)
#define STATUS_DATA_OVERRUN ((NTSTATUS)0xC000003CL)
#define STATUS_DATA_LATE_ERROR ((NTSTATUS)0xC000003DL)
#define STATUS_DATA_ERROR ((NTSTATUS)0xC000003EL)
#define STATUS_CRC_ERROR ((NTSTATUS)0xC000003FL)
#define STATUS_SECTION_TOO_BIG ((NTSTATUS)0xC0000040L)
#define STATUS_PORT_CONNECTION_REFUSED ((NTSTATUS)0xC0000041L)
#define STATUS_INVALID_PORT_HANDLE ((NTSTATUS)0xC0000042L)
#define STATUS_SHARING_VIOLATION ((NTSTATUS)0xC0000043L)
#define STATUS_QUOTA_EXCEEDED ((NTSTATUS)0xC0000044L)
#define STATUS_INVALID_PAGE_PROTECTION ((NTSTATUS)0xC0000045L)
#define STATUS_MUTANT_NOT_OWNED ((NTSTATUS)0xC0000046L)
#define STATUS_SEMAPHORE_LIMIT_EXCEEDED ((NTSTATUS)0xC0000047L)
#define STATUS_PORT_ALREADY_SET ((NTSTATUS)0xC0000048L)
#define STATUS_SECTION_NOT_IMAGE ((NTSTATUS)0xC0000049L)
#define STATUS_SUSPEND_COUNT_EXCEEDED ((NTSTATUS)0xC000004AL)
#define STATUS_THREAD_IS_TERMINATING ((NTSTATUS)0xC000004BL)
#define STATUS_BAD_WORKING_SET_LIMIT ((NTSTATUS)0xC000004CL)
#define STATUS_INCOMPATIBLE_FILE_MAP ((NTSTATUS)0xC000004DL)
#define STATUS_SECTION_PROTECTION ((NTSTATUS)0xC000004EL)
#define STATUS_EAS_NOT_SUPPORTED ((NTSTATUS)0xC000004FL)
#define STATUS_EA_TOO_LARGE ((NTSTATUS)0xC0000050L)
#define STATUS_NONEXISTENT_EA_ENTRY ((NTSTATUS)0xC0000051L)
#define STATUS_NO_EAS_ON_FILE ((NTSTATUS)0xC0000052L)
#define STATUS_EA_CORRUPT_ERROR ((NTSTATUS)0xC0000053L)
#define STATUS_FILE_LOCK_CONFLICT ((NTSTATUS)0xC0000054L)
#define STATUS_LOCK_NOT_GRANTED ((NTSTATUS)0xC0000055L)
#define STATUS_DELETE_PENDING ((NTSTATUS)0xC0000056L)
#define STATUS_CTL_FILE_NOT_SUPPORTED ((NTSTATUS)0xC0000057L)
#define STATUS_UNKNOWN_REVISION ((NTSTATUS)0xC0000058L)
#define STATUS_REVISION_MISMATCH ((NTSTATUS)0xC0000059L)
#define STATUS_INVALID_OWNER ((NTSTATUS)0xC000005AL)
#define STATUS_INVALID_PRIMARY_GROUP ((NTSTATUS)0xC000005BL)
#define STATUS_NO_IMPERSONATION_TOKEN ((NTSTATUS)0xC000005CL)
#define STATUS_CANT_DISABLE_MANDATORY ((NTSTATUS)0xC000005DL)
#define STATUS_NO_LOGON_SERVERS ((NTSTATUS)0xC000005EL)
#define STATUS_NO_SUCH_LOGON_SESSION ((NTSTATUS)0xC000005FL)
#define STATUS_NO_SUCH_PRIVILEGE ((NTSTATUS)0xC0000060L)
#define STATUS_PRIVILEGE_NOT_HELD ((NTSTATUS)0xC0000061L)
#define STATUS_INVALID_ACCOUNT_NAME ((NTSTATUS)0xC0000062L)
#define STATUS_USER_EXISTS ((NTSTATUS)0xC0000063L)
#define STATUS_NO_SUCH_USER ((NTSTATUS)0xC0000064L)
#define STATUS_GROUP_EXISTS ((NTSTATUS)0xC0000065L)
#define STATUS_NO_SUCH_GROUP ((NTSTATUS)0xC0000066L)
#define STATUS_MEMBER_IN_GROUP ((NTSTATUS)0xC0000067L)
#define STATUS_MEMBER_NOT_IN_GROUP ((NTSTATUS)0xC0000068L)
#define STATUS_LAST_ADMIN ((NTSTATUS)0xC0000069L)
#define STATUS_WRONG_PASSWORD ((NTSTATUS)0xC000006AL)
#define STATUS_ILL_FORMED_PASSWORD ((NTSTATUS)0xC000006BL)
#define STATUS_PASSWORD_RESTRICTION ((NTSTATUS)0xC000006CL)
#define STATUS_LOGON_FAILURE ((NTSTATUS)0xC000006DL)
#define STATUS_ACCOUNT_RESTRICTION ((NTSTATUS)0xC000006EL)
#define STATUS_INVALID_LOGON_HOURS ((NTSTATUS)0xC000006FL)
#define STATUS_INVALID_WORKSTATION ((NTSTATUS)0xC0000070L)
#define STATUS_PASSWORD_EXPIRED ((NTSTATUS)0xC0000071L)
#define STATUS_ACCOUNT_DISABLED ((NTSTATUS)0xC0000072L)
#define STATUS_NONE_MAPPED ((NTSTATUS)0xC0000073L)
#define STATUS_TOO_MANY_LUIDS_REQUESTED ((NTSTATUS)0xC0000074L)
#define STATUS_LUIDS_EXHAUSTED ((NTSTATUS)0xC0000075L)
#define STATUS_INVALID_SUB_AUTHORITY ((NTSTATUS)0xC0000076L)
#define STATUS_INVALID_ACL ((NTSTATUS)0xC0000077L)
#define STATUS_INVALID_SID ((NTSTATUS)0xC0000078L)
#define STATUS_INVALID_SECURITY_DESCR ((NTSTATUS)0xC0000079L)
#define STATUS_PROCEDURE_NOT_FOUND ((NTSTATUS)0xC000007AL)
#define STATUS_INVALID_IMAGE_FORMAT ((NTSTATUS)0xC000007BL)
#define STATUS_NO_TOKEN ((NTSTATUS)0xC000007CL)
#define STATUS_BAD_INHERITANCE_ACL ((NTSTATUS)0xC000007DL)
#define STATUS_RANGE_NOT_LOCKED ((NTSTATUS)0xC000007EL)
#define STATUS_DISK_FULL ((NTSTATUS)0xC000007FL)
#define STATUS_SERVER_DISABLED ((NTSTATUS)0xC0000080L)
#define STATUS_SERVER_NOT_DISABLED ((NTSTATUS)0xC0000081L)
#define STATUS_TOO_MANY_GUIDS_REQUESTED ((NTSTATUS)0xC0000082L)
#define STATUS_GUIDS_EXHAUSTED ((NTSTATUS)0xC0000083L)
#define STATUS_INVALID_ID_AUTHORITY ((NTSTATUS)0xC0000084L)
#define STATUS_AGENTS_EXHAUSTED ((NTSTATUS)0xC0000085L)
#define STATUS_INVALID_VOLUME_LABEL ((NTSTATUS)0xC0000086L)
#define STATUS_SECTION_NOT_EXTENDED ((NTSTATUS)0xC0000087L)
#define STATUS_NOT_MAPPED_DATA ((NTSTATUS)0xC0000088L)
#define STATUS_RESOURCE_DATA_NOT_FOUND ((NTSTATUS)0xC0000089L)
#define STATUS_RESOURCE_TYPE_NOT_FOUND ((NTSTATUS)0xC000008AL)
#define STATUS_RESOURCE_NAME_NOT_FOUND ((NTSTATUS)0xC000008BL)
#define STATUS_ARRAY_BOUNDS_EXCEEDED ((NTSTATUS)0xC000008CL)
#define STATUS_FLOAT_DENORMAL_OPERAND ((NTSTATUS)0xC000008DL)
#define STATUS_FLOAT_DIVIDE_BY_ZERO ((NTSTATUS)0xC000008EL)
#define STATUS_FLOAT_INEXACT_RESULT ((NTSTATUS)0xC000008FL)
#define STATUS_FLOAT_INVALID_OPERATION ((NTSTATUS)0xC0000090L)
#define STATUS_FLOAT_OVERFLOW ((NTSTATUS)0xC0000091L)
#define STATUS_FLOAT_STACK_CHECK ((NTSTATUS)0xC0000092L)
#define STATUS_FLOAT_UNDERFLOW ((NTSTATUS)0xC0000093L)
#define STATUS_INTEGER_DIVIDE_BY_ZERO ((NTSTATUS)0xC0000094L)
#define STATUS_INTEGER_OVERFLOW ((NTSTATUS)0xC0000095L)
#define STATUS_PRIVILEGED_INSTRUCTION ((NTSTATUS)0xC0000096L)
#define STATUS_TOO_MANY_PAGING_FILES ((NTSTATUS)0xC0000097L)
#define STATUS_FILE_INVALID ((NTSTATUS)0xC0000098L)
#define STATUS_ALLOTTED_SPACE_EXCEEDED ((NTSTATUS)0xC0000099L)
#define STATUS_INSUFFICIENT_RESOURCES ((NTSTATUS)0xC000009AL)
#define STATUS_DFS_EXIT_PATH_FOUND ((NTSTATUS)0xC000009BL)
#define STATUS_DEVICE_DATA_ERROR ((NTSTATUS)0xC000009CL)
#define STATUS_DEVICE_NOT_CONNECTED ((NTSTATUS)0xC000009DL)
#define STATUS_DEVICE_POWER_FAILURE ((NTSTATUS)0xC000009EL)
#define STATUS_FREE_VM_NOT_AT_BASE ((NTSTATUS)0xC000009FL)
#define STATUS_MEMORY_NOT_ALLOCATED ((NTSTATUS)0xC00000A0L)
#define STATUS_WORKING_SET_QUOTA ((NTSTATUS)0xC00000A1L)
#define STATUS_MEDIA_WRITE_PROTECTED ((NTSTATUS)0xC00000A2L)
#define STATUS_DEVICE_NOT_READY ((NTSTATUS)0xC00000A3L)
#define STATUS_INVALID_GROUP_ATTRIBUTES ((NTSTATUS)0xC00000A4L)
#define STATUS_BAD_IMPERSONATION_LEVEL ((NTSTATUS)0xC00000A5L)
#define STATUS_CANT_OPEN_ANONYMOUS ((NTSTATUS)0xC00000A6L)
#define STATUS_BAD_VALIDATION_CLASS ((NTSTATUS)0xC00000A7L)
#define STATUS_BAD_TOKEN_TYPE ((NTSTATUS)0xC00000A8L)
#define STATUS_BAD_MASTER_BOOT_RECORD ((NTSTATUS)0xC00000A9L)
#define STATUS_INSTRUCTION_MISALIGNMENT ((NTSTATUS)0xC00000AAL)
#define STATUS_INSTANCE_NOT_AVAILABLE ((NTSTATUS)0xC00000ABL)
#define STATUS_PIPE_NOT_AVAILABLE ((NTSTATUS)0xC00000ACL)
#define STATUS_INVALID_PIPE_STATE ((NTSTATUS)0xC00000ADL)
#define STATUS_PIPE_BUSY ((NTSTATUS)0xC00000AEL)
#define STATUS_ILLEGAL_FUNCTION ((NTSTATUS)0xC00000AFL)
#define STATUS_PIPE_DISCONNECTED ((NTSTATUS)0xC00000B0L)
#define STATUS_PIPE_CLOSING ((NTSTATUS)0xC00000B1L)
#define STATUS_PIPE_CONNECTED ((NTSTATUS)0xC00000B2L)
#define STATUS_PIPE_LISTENING ((NTSTATUS)0xC00000B3L)
#define STATUS_INVALID_READ_MODE ((NTSTATUS)0xC00000B4L)
#define STATUS_IO_TIMEOUT ((NTSTATUS)0xC00000B5L)
#define STATUS_FILE_FORCED_CLOSED ((NTSTATUS)0xC00000B6L)
#define STATUS_PROFILING_NOT_STARTED ((NTSTATUS)0xC00000B7L)
#define STATUS_PROFILING_NOT_STOPPED ((NTSTATUS)0xC00000B8L)
#define STATUS_COULD_NOT_INTERPRET ((NTSTATUS)0xC00000B9L)
#define STATUS_FILE_IS_A_DIRECTORY ((NTSTATUS)0xC00000BAL)
#define STATUS_NOT_SUPPORTED ((NTSTATUS)0xC00000BBL)
#define STATUS_REMOTE_NOT_LISTENING ((NTSTATUS)0xC00000BCL)
#define STATUS_DUPLICATE_NAME ((NTSTATUS)0xC00000BDL)
#define STATUS_BAD_NETWORK_PATH ((NTSTATUS)0xC00000BEL)
#define STATUS_NETWORK_BUSY ((NTSTATUS)0xC00000BFL)
#define STATUS_DEVICE_DOES_NOT_EXIST ((NTSTATUS)0xC00000C0L)
#define STATUS_TOO_MANY_COMMANDS ((NTSTATUS)0xC00000C1L)
#define STATUS_ADAPTER_HARDWARE_ERROR ((NTSTATUS)0xC00000C2L)
#define STATUS_INVALID_NETWORK_RESPONSE ((NTSTATUS)0xC00000C3L)
#define STATUS_UNEXPECTED_NETWORK_ERROR ((NTSTATUS)0xC00000C4L)
#define STATUS_BAD_REMOTE_ADAPTER ((NTSTATUS)0xC00000C5L)
#define STATUS_PRINT_QUEUE_FULL ((NTSTATUS)0xC00000C6L)
#define STATUS_NO_SPOOL_SPACE ((NTSTATUS)0xC00000C7L)
#define STATUS_PRINT_CANCELLED ((NTSTATUS)0xC00000C8L)
#define STATUS_NETWORK_NAME_DELETED ((NTSTATUS)0xC00000C9L)
#define STATUS_NETWORK_ACCESS_DENIED ((NTSTATUS)0xC00000CAL)
#define STATUS_BAD_DEVICE_TYPE ((NTSTATUS)0xC00000CBL)
#define STATUS_BAD_NETWORK_NAME ((NTSTATUS)0xC00000CCL)
#define STATUS_TOO_MANY_NAMES ((NTSTATUS)0xC00000CDL)
#define STATUS_TOO_MANY_SESSIONS ((NTSTATUS)0xC00000CEL)
#define STATUS_SHARING_PAUSED ((NTSTATUS)0xC00000CFL)
#define STATUS_REQUEST_NOT_ACCEPTED ((NTSTATUS)0xC00000D0L)
#define STATUS_REDIRECTOR_PAUSED ((NTSTATUS)0xC00000D1L)
#define STATUS_NET_WRITE_FAULT ((NTSTATUS)0xC00000D2L)
#define STATUS_PROFILING_AT_LIMIT ((NTSTATUS)0xC00000D3L)
#define STATUS_NOT_SAME_DEVICE ((NTSTATUS)0xC00000D4L)
#define STATUS_FILE_RENAMED ((NTSTATUS)0xC00000D5L)
#define STATUS_VIRTUAL_CIRCUIT_CLOSED ((NTSTATUS)0xC00000D6L)
#define STATUS_NO_SECURITY_ON_OBJECT ((NTSTATUS)0xC00000D7L)
#define STATUS_CANT_WAIT ((NTSTATUS)0xC00000D8L)
#define STATUS_PIPE_EMPTY ((NTSTATUS)0xC00000D9L)
#define STATUS_CANT_ACCESS_DOMAIN_INFO ((NTSTATUS)0xC00000DAL)
#define STATUS_CANT_TERMINATE_SELF ((NTSTATUS)0xC00000DBL)
#define STATUS_INVALID_SERVER_STATE ((NTSTATUS)0xC00000DCL)
#define STATUS_INVALID_DOMAIN_STATE ((NTSTATUS)0xC00000DDL)
#define STATUS_INVALID_DOMAIN_ROLE ((NTSTATUS)0xC00000DEL)
#define STATUS_NO_SUCH_DOMAIN ((NTSTATUS)0xC00000DFL)
#define STATUS_DOMAIN_EXISTS ((NTSTATUS)0xC00000E0L)
#define STATUS_DOMAIN_LIMIT_EXCEEDED ((NTSTATUS)0xC00000E1L)
#define STATUS_OPLOCK_NOT_GRANTED ((NTSTATUS)0xC00000E2L)
#define STATUS_INVALID_OPLOCK_PROTOCOL ((NTSTATUS)0xC00000E3L)
#define STATUS_INTERNAL_DB_CORRUPTION ((NTSTATUS)0xC00000E4L)
#define STATUS_INTERNAL_ERROR ((NTSTATUS)0xC00000E5L)
#define STATUS_GENERIC_NOT_MAPPED ((NTSTATUS)0xC00000E6L)
#define STATUS_BAD_DESCRIPTOR_FORMAT ((NTSTATUS)0xC00000E7L)
#define STATUS_INVALID_USER_BUFFER ((NTSTATUS)0xC00000E8L)
#define STATUS_UNEXPECTED_IO_ERROR ((NTSTATUS)0xC00000E9L)
#define STATUS_UNEXPECTED_MM_CREATE_ERR ((NTSTATUS)0xC00000EAL)
#define STATUS_UNEXPECTED_MM_MAP_ERROR ((NTSTATUS)0xC00000EBL)
#define STATUS_UNEXPECTED_MM_EXTEND_ERR ((NTSTATUS)0xC00000ECL)
#define STATUS_NOT_LOGON_PROCESS ((NTSTATUS)0xC00000EDL)
#define STATUS_LOGON_SESSION_EXISTS ((NTSTATUS)0xC00000EEL)
#define STATUS_INVALID_PARAMETER_1 ((NTSTATUS)0xC00000EFL)
#define STATUS_INVALID_PARAMETER_2 ((NTSTATUS)0xC00000F0L)
#define STATUS_INVALID_PARAMETER_3 ((NTSTATUS)0xC00000F1L)
#define STATUS_INVALID_PARAMETER_4 ((NTSTATUS)0xC00000F2L)
#define STATUS_INVALID_PARAMETER_5 ((NTSTATUS)0xC00000F3L)
#define STATUS_INVALID_PARAMETER_6 ((NTSTATUS)0xC00000F4L)
#define STATUS_INVALID_PARAMETER_7 ((NTSTATUS)0xC00000F5L)
#define STATUS_INVALID_PARAMETER_8 ((NTSTATUS)0xC00000F6L)
#define STATUS_INVALID_PARAMETER_9 ((NTSTATUS)0xC00000F7L)
#define STATUS_INVALID_PARAMETER_10 ((NTSTATUS)0xC00000F8L)
#define STATUS_INVALID_PARAMETER_11 ((NTSTATUS)0xC00000F9L)
#define STATUS_INVALID_PARAMETER_12 ((NTSTATUS)0xC00000FAL)
#define STATUS_REDIRECTOR_NOT_STARTED ((NTSTATUS)0xC00000FBL)
#define STATUS_REDIRECTOR_STARTED ((NTSTATUS)0xC00000FCL)
#define STATUS_STACK_OVERFLOW ((NTSTATUS)0xC00000FDL)
#define STATUS_NO_SUCH_PACKAGE ((NTSTATUS)0xC00000FEL)
#define STATUS_BAD_FUNCTION_TABLE ((NTSTATUS)0xC00000FFL)
#define STATUS_VARIABLE_NOT_FOUND ((NTSTATUS)0xC0000100L)
#define STATUS_DIRECTORY_NOT_EMPTY ((NTSTATUS)0xC0000101L)
#define STATUS_FILE_CORRUPT_ERROR ((NTSTATUS)0xC0000102L)
#define STATUS_NOT_A_DIRECTORY ((NTSTATUS)0xC0000103L)
#define STATUS_BAD_LOGON_SESSION_STATE ((NTSTATUS)0xC0000104L)
#define STATUS_LOGON_SESSION_COLLISION ((NTSTATUS)0xC0000105L)
#define STATUS_NAME_TOO_LONG ((NTSTATUS)0xC0000106L)
#define STATUS_FILES_OPEN ((NTSTATUS)0xC0000107L)
#define STATUS_CONNECTION_IN_USE ((NTSTATUS)0xC0000108L)
#define STATUS_MESSAGE_NOT_FOUND ((NTSTATUS)0xC0000109L)
#define STATUS_PROCESS_IS_TERMINATING ((NTSTATUS)0xC000010AL)
#define STATUS_INVALID_LOGON_TYPE ((NTSTATUS)0xC000010BL)
#define STATUS_NO_GUID_TRANSLATION ((NTSTATUS)0xC000010CL)
#define STATUS_CANNOT_IMPERSONATE ((NTSTATUS)0xC000010DL)
#define STATUS_IMAGE_ALREADY_LOADED ((NTSTATUS)0xC000010EL)
#define STATUS_ABIOS_NOT_PRESENT ((NTSTATUS)0xC000010FL)
#define STATUS_ABIOS_LID_NOT_EXIST ((NTSTATUS)0xC0000110L)
#define STATUS_ABIOS_LID_ALREADY_OWNED ((NTSTATUS)0xC0000111L)
#define STATUS_ABIOS_NOT_LID_OWNER ((NTSTATUS)0xC0000112L)
#define STATUS_ABIOS_INVALID_COMMAND ((NTSTATUS)0xC0000113L)
#define STATUS_ABIOS_INVALID_LID ((NTSTATUS)0xC0000114L)
#define STATUS_ABIOS_SELECTOR_NOT_AVAILABLE ((NTSTATUS)0xC0000115L)
#define STATUS_ABIOS_INVALID_SELECTOR ((NTSTATUS)0xC0000116L)
#define STATUS_NO_LDT ((NTSTATUS)0xC0000117L)
#define STATUS_INVALID_LDT_SIZE ((NTSTATUS)0xC0000118L)
#define STATUS_INVALID_LDT_OFFSET ((NTSTATUS)0xC0000119L)
#define STATUS_INVALID_LDT_DESCRIPTOR ((NTSTATUS)0xC000011AL)
#define STATUS_INVALID_IMAGE_NE_FORMAT ((NTSTATUS)0xC000011BL)
#define STATUS_RXACT_INVALID_STATE ((NTSTATUS)0xC000011CL)
#define STATUS_RXACT_COMMIT_FAILURE ((NTSTATUS)0xC000011DL)
#define STATUS_MAPPED_FILE_SIZE_ZERO ((NTSTATUS)0xC000011EL)
#define STATUS_TOO_MANY_OPENED_FILES ((NTSTATUS)0xC000011FL)
#define STATUS_CANCELLED ((NTSTATUS)0xC0000120L)
#define STATUS_CANNOT_DELETE ((NTSTATUS)0xC0000121L)
#define STATUS_INVALID_COMPUTER_NAME ((NTSTATUS)0xC0000122L)
#define STATUS_FILE_DELETED ((NTSTATUS)0xC0000123L)
#define STATUS_SPECIAL_ACCOUNT ((NTSTATUS)0xC0000124L)
#define STATUS_SPECIAL_GROUP ((NTSTATUS)0xC0000125L)
#define STATUS_SPECIAL_USER ((NTSTATUS)0xC0000126L)
#define STATUS_MEMBERS_PRIMARY_GROUP ((NTSTATUS)0xC0000127L)
#define STATUS_FILE_CLOSED ((NTSTATUS)0xC0000128L)
#define STATUS_TOO_MANY_THREADS ((NTSTATUS)0xC0000129L)
#define STATUS_THREAD_NOT_IN_PROCESS ((NTSTATUS)0xC000012AL)
#define STATUS_TOKEN_ALREADY_IN_USE ((NTSTATUS)0xC000012BL)
#define STATUS_PAGEFILE_QUOTA_EXCEEDED ((NTSTATUS)0xC000012CL)
#define STATUS_COMMITMENT_LIMIT ((NTSTATUS)0xC000012DL)
#define STATUS_INVALID_IMAGE_LE_FORMAT ((NTSTATUS)0xC000012EL)
#define STATUS_INVALID_IMAGE_NOT_MZ ((NTSTATUS)0xC000012FL)
#define STATUS_INVALID_IMAGE_PROTECT ((NTSTATUS)0xC0000130L)
#define STATUS_INVALID_IMAGE_WIN_16 ((NTSTATUS)0xC0000131L)
#define STATUS_LOGON_SERVER_CONFLICT ((NTSTATUS)0xC0000132L)
#define STATUS_TIME_DIFFERENCE_AT_DC ((NTSTATUS)0xC0000133L)
#define STATUS_SYNCHRONIZATION_REQUIRED ((NTSTATUS)0xC0000134L)
#define STATUS_DLL_NOT_FOUND ((NTSTATUS)0xC0000135L)
#define STATUS_OPEN_FAILED ((NTSTATUS)0xC0000136L)
#define STATUS_IO_PRIVILEGE_FAILED ((NTSTATUS)0xC0000137L)
#define STATUS_ORDINAL_NOT_FOUND ((NTSTATUS)0xC0000138L)
#define STATUS_ENTRYPOINT_NOT_FOUND ((NTSTATUS)0xC0000139L)
#define STATUS_CONTROL_C_EXIT ((NTSTATUS)0xC000013AL)
#define STATUS_LOCAL_DISCONNECT ((NTSTATUS)0xC000013BL)
#define STATUS_REMOTE_DISCONNECT ((NTSTATUS)0xC000013CL)
#define STATUS_REMOTE_RESOURCES ((NTSTATUS)0xC000013DL)
#define STATUS_LINK_FAILED ((NTSTATUS)0xC000013EL)
#define STATUS_LINK_TIMEOUT ((NTSTATUS)0xC000013FL)
#define STATUS_INVALID_CONNECTION ((NTSTATUS)0xC0000140L)
#define STATUS_INVALID_ADDRESS ((NTSTATUS)0xC0000141L)
#define STATUS_DLL_INIT_FAILED ((NTSTATUS)0xC0000142L)
#define STATUS_MISSING_SYSTEMFILE ((NTSTATUS)0xC0000143L)
#define STATUS_UNHANDLED_EXCEPTION ((NTSTATUS)0xC0000144L)
#define STATUS_APP_INIT_FAILURE ((NTSTATUS)0xC0000145L)
#define STATUS_PAGEFILE_CREATE_FAILED ((NTSTATUS)0xC0000146L)
#define STATUS_NO_PAGEFILE ((NTSTATUS)0xC0000147L)
#define STATUS_INVALID_LEVEL ((NTSTATUS)0xC0000148L)
#define STATUS_WRONG_PASSWORD_CORE ((NTSTATUS)0xC0000149L)
#define STATUS_ILLEGAL_FLOAT_CONTEXT ((NTSTATUS)0xC000014AL)
#define STATUS_PIPE_BROKEN ((NTSTATUS)0xC000014BL)
#define STATUS_REGISTRY_CORRUPT ((NTSTATUS)0xC000014CL)
#define STATUS_REGISTRY_IO_FAILED ((NTSTATUS)0xC000014DL)
#define STATUS_NO_EVENT_PAIR ((NTSTATUS)0xC000014EL)
#define STATUS_UNRECOGNIZED_VOLUME ((NTSTATUS)0xC000014FL)
#define STATUS_SERIAL_NO_DEVICE_INITED ((NTSTATUS)0xC0000150L)
#define STATUS_NO_SUCH_ALIAS ((NTSTATUS)0xC0000151L)
#define STATUS_MEMBER_NOT_IN_ALIAS ((NTSTATUS)0xC0000152L)
#define STATUS_MEMBER_IN_ALIAS ((NTSTATUS)0xC0000153L)
#define STATUS_ALIAS_EXISTS ((NTSTATUS)0xC0000154L)
#define STATUS_LOGON_NOT_GRANTED ((NTSTATUS)0xC0000155L)
#define STATUS_TOO_MANY_SECRETS ((NTSTATUS)0xC0000156L)
#define STATUS_SECRET_TOO_LONG ((NTSTATUS)0xC0000157L)
#define STATUS_INTERNAL_DB_ERROR ((NTSTATUS)0xC0000158L)
#define STATUS_FULLSCREEN_MODE ((NTSTATUS)0xC0000159L)
#define STATUS_TOO_MANY_CONTEXT_IDS ((NTSTATUS)0xC000015AL)
#define STATUS_LOGON_TYPE_NOT_GRANTED ((NTSTATUS)0xC000015BL)
#define STATUS_NOT_REGISTRY_FILE ((NTSTATUS)0xC000015CL)
#define STATUS_NT_CROSS_ENCRYPTION_REQUIRED ((NTSTATUS)0xC000015DL)
#define STATUS_DOMAIN_CTRLR_CONFIG_ERROR ((NTSTATUS)0xC000015EL)
#define STATUS_FT_MISSING_MEMBER ((NTSTATUS)0xC000015FL)
#define STATUS_ILL_FORMED_SERVICE_ENTRY ((NTSTATUS)0xC0000160L)
#define STATUS_ILLEGAL_CHARACTER ((NTSTATUS)0xC0000161L)
#define STATUS_UNMAPPABLE_CHARACTER ((NTSTATUS)0xC0000162L)
#define STATUS_UNDEFINED_CHARACTER ((NTSTATUS)0xC0000163L)
#define STATUS_FLOPPY_VOLUME ((NTSTATUS)0xC0000164L)
#define STATUS_FLOPPY_ID_MARK_NOT_FOUND ((NTSTATUS)0xC0000165L)
#define STATUS_FLOPPY_WRONG_CYLINDER ((NTSTATUS)0xC0000166L)
#define STATUS_FLOPPY_UNKNOWN_ERROR ((NTSTATUS)0xC0000167L)
#define STATUS_FLOPPY_BAD_REGISTERS ((NTSTATUS)0xC0000168L)
#define STATUS_DISK_RECALIBRATE_FAILED ((NTSTATUS)0xC0000169L)
#define STATUS_DISK_OPERATION_FAILED ((NTSTATUS)0xC000016AL)
#define STATUS_DISK_RESET_FAILED ((NTSTATUS)0xC000016BL)
#define STATUS_SHARED_IRQ_BUSY ((NTSTATUS)0xC000016CL)
#define STATUS_FT_ORPHANING ((NTSTATUS)0xC000016DL)
#define STATUS_BIOS_FAILED_TO_CONNECT_INTERRUPT ((NTSTATUS)0xC000016EL)
#define STATUS_PARTITION_FAILURE ((NTSTATUS)0xC0000172L)
#define STATUS_INVALID_BLOCK_LENGTH ((NTSTATUS)0xC0000173L)
#define STATUS_DEVICE_NOT_PARTITIONED ((NTSTATUS)0xC0000174L)
#define STATUS_UNABLE_TO_LOCK_MEDIA ((NTSTATUS)0xC0000175L)
#define STATUS_UNABLE_TO_UNLOAD_MEDIA ((NTSTATUS)0xC0000176L)
#define STATUS_EOM_OVERFLOW ((NTSTATUS)0xC0000177L)
#define STATUS_NO_MEDIA ((NTSTATUS)0xC0000178L)
#define STATUS_NO_SUCH_MEMBER ((NTSTATUS)0xC000017AL)
#define STATUS_INVALID_MEMBER ((NTSTATUS)0xC000017BL)
#define STATUS_KEY_DELETED ((NTSTATUS)0xC000017CL)
#define STATUS_NO_LOG_SPACE ((NTSTATUS)0xC000017DL)
#define STATUS_TOO_MANY_SIDS ((NTSTATUS)0xC000017EL)
#define STATUS_LM_CROSS_ENCRYPTION_REQUIRED ((NTSTATUS)0xC000017FL)
#define STATUS_KEY_HAS_CHILDREN ((NTSTATUS)0xC0000180L)
#define STATUS_CHILD_MUST_BE_VOLATILE ((NTSTATUS)0xC0000181L)
#define STATUS_DEVICE_CONFIGURATION_ERROR ((NTSTATUS)0xC0000182L)
#define STATUS_DRIVER_INTERNAL_ERROR ((NTSTATUS)0xC0000183L)
#define STATUS_INVALID_DEVICE_STATE ((NTSTATUS)0xC0000184L)
#define STATUS_IO_DEVICE_ERROR ((NTSTATUS)0xC0000185L)
#define STATUS_DEVICE_PROTOCOL_ERROR ((NTSTATUS)0xC0000186L)
#define STATUS_BACKUP_CONTROLLER ((NTSTATUS)0xC0000187L)
#define STATUS_LOG_FILE_FULL ((NTSTATUS)0xC0000188L)
#define STATUS_TOO_LATE ((NTSTATUS)0xC0000189L)
#define STATUS_NO_TRUST_LSA_SECRET ((NTSTATUS)0xC000018AL)
#define STATUS_NO_TRUST_SAM_ACCOUNT ((NTSTATUS)0xC000018BL)
#define STATUS_TRUSTED_DOMAIN_FAILURE ((NTSTATUS)0xC000018CL)
#define STATUS_TRUSTED_RELATIONSHIP_FAILURE ((NTSTATUS)0xC000018DL)
#define STATUS_EVENTLOG_FILE_CORRUPT ((NTSTATUS)0xC000018EL)
#define STATUS_EVENTLOG_CANT_START ((NTSTATUS)0xC000018FL)
#define STATUS_TRUST_FAILURE ((NTSTATUS)0xC0000190L)
#define STATUS_MUTANT_LIMIT_EXCEEDED ((NTSTATUS)0xC0000191L)
#define STATUS_NETLOGON_NOT_STARTED ((NTSTATUS)0xC0000192L)
#define STATUS_ACCOUNT_EXPIRED ((NTSTATUS)0xC0000193L)
#define STATUS_POSSIBLE_DEADLOCK ((NTSTATUS)0xC0000194L)
#define STATUS_NETWORK_CREDENTIAL_CONFLICT ((NTSTATUS)0xC0000195L)
#define STATUS_REMOTE_SESSION_LIMIT ((NTSTATUS)0xC0000196L)
#define STATUS_EVENTLOG_FILE_CHANGED ((NTSTATUS)0xC0000197L)
#define STATUS_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT ((NTSTATUS)0xC0000198L)
#define STATUS_NOLOGON_WORKSTATION_TRUST_ACCOUNT ((NTSTATUS)0xC0000199L)
#define STATUS_NOLOGON_SERVER_TRUST_ACCOUNT ((NTSTATUS)0xC000019AL)
#define STATUS_DOMAIN_TRUST_INCONSISTENT ((NTSTATUS)0xC000019BL)
#define STATUS_FS_DRIVER_REQUIRED ((NTSTATUS)0xC000019CL)
#define STATUS_NO_USER_SESSION_KEY ((NTSTATUS)0xC0000202L)
#define STATUS_USER_SESSION_DELETED ((NTSTATUS)0xC0000203L)
#define STATUS_RESOURCE_LANG_NOT_FOUND ((NTSTATUS)0xC0000204L)
#define STATUS_INSUFF_SERVER_RESOURCES ((NTSTATUS)0xC0000205L)
#define STATUS_INVALID_BUFFER_SIZE ((NTSTATUS)0xC0000206L)
#define STATUS_INVALID_ADDRESS_COMPONENT ((NTSTATUS)0xC0000207L)
#define STATUS_INVALID_ADDRESS_WILDCARD ((NTSTATUS)0xC0000208L)
#define STATUS_TOO_MANY_ADDRESSES ((NTSTATUS)0xC0000209L)
#define STATUS_ADDRESS_ALREADY_EXISTS ((NTSTATUS)0xC000020AL)
#define STATUS_ADDRESS_CLOSED ((NTSTATUS)0xC000020BL)
#define STATUS_CONNECTION_DISCONNECTED ((NTSTATUS)0xC000020CL)
#define STATUS_CONNECTION_RESET ((NTSTATUS)0xC000020DL)
#define STATUS_TOO_MANY_NODES ((NTSTATUS)0xC000020EL)
#define STATUS_TRANSACTION_ABORTED ((NTSTATUS)0xC000020FL)
#define STATUS_TRANSACTION_TIMED_OUT ((NTSTATUS)0xC0000210L)
#define STATUS_TRANSACTION_NO_RELEASE ((NTSTATUS)0xC0000211L)
#define STATUS_TRANSACTION_NO_MATCH ((NTSTATUS)0xC0000212L)
#define STATUS_TRANSACTION_RESPONDED ((NTSTATUS)0xC0000213L)
#define STATUS_TRANSACTION_INVALID_ID ((NTSTATUS)0xC0000214L)
#define STATUS_TRANSACTION_INVALID_TYPE ((NTSTATUS)0xC0000215L)
#define STATUS_NOT_SERVER_SESSION ((NTSTATUS)0xC0000216L)
#define STATUS_NOT_CLIENT_SESSION ((NTSTATUS)0xC0000217L)
#define STATUS_CANNOT_LOAD_REGISTRY_FILE ((NTSTATUS)0xC0000218L)
#define STATUS_DEBUG_ATTACH_FAILED ((NTSTATUS)0xC0000219L)
#define STATUS_SYSTEM_PROCESS_TERMINATED ((NTSTATUS)0xC000021AL)
#define STATUS_DATA_NOT_ACCEPTED ((NTSTATUS)0xC000021BL)
#define STATUS_NO_BROWSER_SERVERS_FOUND ((NTSTATUS)0xC000021CL)
#define STATUS_VDM_HARD_ERROR ((NTSTATUS)0xC000021DL)
#define STATUS_DRIVER_CANCEL_TIMEOUT ((NTSTATUS)0xC000021EL)
#define STATUS_REPLY_MESSAGE_MISMATCH ((NTSTATUS)0xC000021FL)
#define STATUS_MAPPED_ALIGNMENT ((NTSTATUS)0xC0000220L)
#define STATUS_IMAGE_CHECKSUM_MISMATCH ((NTSTATUS)0xC0000221L)
#define STATUS_LOST_WRITEBEHIND_DATA ((NTSTATUS)0xC0000222L)
#define STATUS_CLIENT_SERVER_PARAMETERS_INVALID ((NTSTATUS)0xC0000223L)
#define STATUS_PASSWORD_MUST_CHANGE ((NTSTATUS)0xC0000224L)
#define STATUS_NOT_FOUND ((NTSTATUS)0xC0000225L)
#define STATUS_NOT_TINY_STREAM ((NTSTATUS)0xC0000226L)
#define STATUS_RECOVERY_FAILURE ((NTSTATUS)0xC0000227L)
#define STATUS_STACK_OVERFLOW_READ ((NTSTATUS)0xC0000228L)
#define STATUS_FAIL_CHECK ((NTSTATUS)0xC0000229L)
#define STATUS_DUPLICATE_OBJECTID ((NTSTATUS)0xC000022AL)
#define STATUS_OBJECTID_EXISTS ((NTSTATUS)0xC000022BL)
#define STATUS_CONVERT_TO_LARGE ((NTSTATUS)0xC000022CL)
#define STATUS_RETRY ((NTSTATUS)0xC000022DL)
#define STATUS_FOUND_OUT_OF_SCOPE ((NTSTATUS)0xC000022EL)
#define STATUS_ALLOCATE_BUCKET ((NTSTATUS)0xC000022FL)
#define STATUS_PROPSET_NOT_FOUND ((NTSTATUS)0xC0000230L)
#define STATUS_MARSHALL_OVERFLOW ((NTSTATUS)0xC0000231L)
#define STATUS_INVALID_VARIANT ((NTSTATUS)0xC0000232L)
#define STATUS_DOMAIN_CONTROLLER_NOT_FOUND ((NTSTATUS)0xC0000233L)
#define STATUS_ACCOUNT_LOCKED_OUT ((NTSTATUS)0xC0000234L)
#define STATUS_HANDLE_NOT_CLOSABLE ((NTSTATUS)0xC0000235L)
#define STATUS_CONNECTION_REFUSED ((NTSTATUS)0xC0000236L)
#define STATUS_GRACEFUL_DISCONNECT ((NTSTATUS)0xC0000237L)
#define STATUS_ADDRESS_ALREADY_ASSOCIATED ((NTSTATUS)0xC0000238L)
#define STATUS_ADDRESS_NOT_ASSOCIATED ((NTSTATUS)0xC0000239L)
#define STATUS_CONNECTION_INVALID ((NTSTATUS)0xC000023AL)
#define STATUS_CONNECTION_ACTIVE ((NTSTATUS)0xC000023BL)
#define STATUS_NETWORK_UNREACHABLE ((NTSTATUS)0xC000023CL)
#define STATUS_HOST_UNREACHABLE ((NTSTATUS)0xC000023DL)
#define STATUS_PROTOCOL_UNREACHABLE ((NTSTATUS)0xC000023EL)
#define STATUS_PORT_UNREACHABLE ((NTSTATUS)0xC000023FL)
#define STATUS_REQUEST_ABORTED ((NTSTATUS)0xC0000240L)
#define STATUS_CONNECTION_ABORTED ((NTSTATUS)0xC0000241L)
#define STATUS_BAD_COMPRESSION_BUFFER ((NTSTATUS)0xC0000242L)
#define STATUS_USER_MAPPED_FILE ((NTSTATUS)0xC0000243L)
#define STATUS_AUDIT_FAILED ((NTSTATUS)0xC0000244L)
#define STATUS_TIMER_RESOLUTION_NOT_SET ((NTSTATUS)0xC0000245L)
#define STATUS_CONNECTION_COUNT_LIMIT ((NTSTATUS)0xC0000246L)
#define STATUS_LOGIN_TIME_RESTRICTION ((NTSTATUS)0xC0000247L)
#define STATUS_LOGIN_WKSTA_RESTRICTION ((NTSTATUS)0xC0000248L)
#define STATUS_IMAGE_MP_UP_MISMATCH ((NTSTATUS)0xC0000249L)
#define STATUS_INSUFFICIENT_LOGON_INFO ((NTSTATUS)0xC0000250L)
#define STATUS_BAD_DLL_ENTRYPOINT ((NTSTATUS)0xC0000251L)
#define STATUS_BAD_SERVICE_ENTRYPOINT ((NTSTATUS)0xC0000252L)
#define STATUS_LPC_REPLY_LOST ((NTSTATUS)0xC0000253L)
#define STATUS_IP_ADDRESS_CONFLICT1 ((NTSTATUS)0xC0000254L)
#define STATUS_IP_ADDRESS_CONFLICT2 ((NTSTATUS)0xC0000255L)
#define STATUS_REGISTRY_QUOTA_LIMIT ((NTSTATUS)0xC0000256L)
#define STATUS_PATH_NOT_COVERED ((NTSTATUS)0xC0000257L)
#define STATUS_NO_CALLBACK_ACTIVE ((NTSTATUS)0xC0000258L)
#define STATUS_LICENSE_QUOTA_EXCEEDED ((NTSTATUS)0xC0000259L)
#define STATUS_PWD_TOO_SHORT ((NTSTATUS)0xC000025AL)
#define STATUS_PWD_TOO_RECENT ((NTSTATUS)0xC000025BL)
#define STATUS_PWD_HISTORY_CONFLICT ((NTSTATUS)0xC000025CL)
#define STATUS_PLUGPLAY_NO_DEVICE ((NTSTATUS)0xC000025EL)
#define STATUS_UNSUPPORTED_COMPRESSION ((NTSTATUS)0xC000025FL)
#define STATUS_INVALID_HW_PROFILE ((NTSTATUS)0xC0000260L)
#define STATUS_INVALID_PLUGPLAY_DEVICE_PATH ((NTSTATUS)0xC0000261L)
#define STATUS_DRIVER_ORDINAL_NOT_FOUND ((NTSTATUS)0xC0000262L)
#define STATUS_DRIVER_ENTRYPOINT_NOT_FOUND ((NTSTATUS)0xC0000263L)
#define STATUS_RESOURCE_NOT_OWNED ((NTSTATUS)0xC0000264L)
#define STATUS_TOO_MANY_LINKS ((NTSTATUS)0xC0000265L)
#define STATUS_QUOTA_LIST_INCONSISTENT ((NTSTATUS)0xC0000266L)
#define STATUS_FILE_IS_OFFLINE ((NTSTATUS)0xC0000267L)
#define STATUS_EVALUATION_EXPIRATION ((NTSTATUS)0xC0000268L)
#define STATUS_ILLEGAL_DLL_RELOCATION ((NTSTATUS)0xC0000269L)
#define STATUS_LICENSE_VIOLATION ((NTSTATUS)0xC000026AL)
#define STATUS_DLL_INIT_FAILED_LOGOFF ((NTSTATUS)0xC000026BL)
#define STATUS_DRIVER_UNABLE_TO_LOAD ((NTSTATUS)0xC000026CL)
#define STATUS_DFS_UNAVAILABLE ((NTSTATUS)0xC000026DL)
#define STATUS_VOLUME_DISMOUNTED ((NTSTATUS)0xC000026EL)
#define STATUS_WX86_INTERNAL_ERROR ((NTSTATUS)0xC000026FL)
#define STATUS_WX86_FLOAT_STACK_CHECK ((NTSTATUS)0xC0000270L)
#define STATUS_VALIDATE_CONTINUE ((NTSTATUS)0xC0000271L)
#define STATUS_NO_MATCH ((NTSTATUS)0xC0000272L)
#define STATUS_NO_MORE_MATCHES ((NTSTATUS)0xC0000273L)
#define STATUS_NOT_A_REPARSE_POINT ((NTSTATUS)0xC0000275L)
#define STATUS_IO_REPARSE_TAG_INVALID ((NTSTATUS)0xC0000276L)
#define STATUS_IO_REPARSE_TAG_MISMATCH ((NTSTATUS)0xC0000277L)
#define STATUS_IO_REPARSE_DATA_INVALID ((NTSTATUS)0xC0000278L)
#define STATUS_IO_REPARSE_TAG_NOT_HANDLED ((NTSTATUS)0xC0000279L)
#define STATUS_REPARSE_POINT_NOT_RESOLVED ((NTSTATUS)0xC0000280L)
#define STATUS_DIRECTORY_IS_A_REPARSE_POINT ((NTSTATUS)0xC0000281L)
#define STATUS_RANGE_LIST_CONFLICT ((NTSTATUS)0xC0000282L)
#define STATUS_SOURCE_ELEMENT_EMPTY ((NTSTATUS)0xC0000283L)
#define STATUS_DESTINATION_ELEMENT_FULL ((NTSTATUS)0xC0000284L)
#define STATUS_ILLEGAL_ELEMENT_ADDRESS ((NTSTATUS)0xC0000285L)
#define STATUS_MAGAZINE_NOT_PRESENT ((NTSTATUS)0xC0000286L)
#define STATUS_REINITIALIZATION_NEEDED ((NTSTATUS)0xC0000287L)
#define STATUS_DEVICE_REQUIRES_CLEANING ((NTSTATUS)0x80000288L)
#define STATUS_DEVICE_DOOR_OPEN ((NTSTATUS)0x80000289L)
#define STATUS_ENCRYPTION_FAILED ((NTSTATUS)0xC000028AL)
#define STATUS_DECRYPTION_FAILED ((NTSTATUS)0xC000028BL)
#define STATUS_RANGE_NOT_FOUND ((NTSTATUS)0xC000028CL)
#define STATUS_NO_RECOVERY_POLICY ((NTSTATUS)0xC000028DL)
#define STATUS_NO_EFS ((NTSTATUS)0xC000028EL)
#define STATUS_WRONG_EFS ((NTSTATUS)0xC000028FL)
#define STATUS_NO_USER_KEYS ((NTSTATUS)0xC0000290L)
#define STATUS_FILE_NOT_ENCRYPTED ((NTSTATUS)0xC0000291L)
#define STATUS_NOT_EXPORT_FORMAT ((NTSTATUS)0xC0000292L)
#define STATUS_FILE_ENCRYPTED ((NTSTATUS)0xC0000293L)
#define STATUS_WAKE_SYSTEM ((NTSTATUS)0x40000294L)
#define STATUS_WMI_GUID_NOT_FOUND ((NTSTATUS)0xC0000295L)
#define STATUS_WMI_INSTANCE_NOT_FOUND ((NTSTATUS)0xC0000296L)
#define STATUS_WMI_ITEMID_NOT_FOUND ((NTSTATUS)0xC0000297L)
#define STATUS_WMI_TRY_AGAIN ((NTSTATUS)0xC0000298L)
#define STATUS_SHARED_POLICY ((NTSTATUS)0xC0000299L)
#define STATUS_POLICY_OBJECT_NOT_FOUND ((NTSTATUS)0xC000029AL)
#define STATUS_POLICY_ONLY_IN_DS ((NTSTATUS)0xC000029BL)
#define STATUS_VOLUME_NOT_UPGRADED ((NTSTATUS)0xC000029CL)
#define STATUS_REMOTE_STORAGE_NOT_ACTIVE ((NTSTATUS)0xC000029DL)
#define STATUS_REMOTE_STORAGE_MEDIA_ERROR ((NTSTATUS)0xC000029EL)
#define STATUS_NO_TRACKING_SERVICE ((NTSTATUS)0xC000029FL)
#define STATUS_SERVER_SID_MISMATCH ((NTSTATUS)0xC00002A0L)
#define STATUS_DS_NO_ATTRIBUTE_OR_VALUE ((NTSTATUS)0xC00002A1L)
#define STATUS_DS_INVALID_ATTRIBUTE_SYNTAX ((NTSTATUS)0xC00002A2L)
#define STATUS_DS_ATTRIBUTE_TYPE_UNDEFINED ((NTSTATUS)0xC00002A3L)
#define STATUS_DS_ATTRIBUTE_OR_VALUE_EXISTS ((NTSTATUS)0xC00002A4L)
#define STATUS_DS_BUSY ((NTSTATUS)0xC00002A5L)
#define STATUS_DS_UNAVAILABLE ((NTSTATUS)0xC00002A6L)
#define STATUS_DS_NO_RIDS_ALLOCATED ((NTSTATUS)0xC00002A7L)
#define STATUS_DS_NO_MORE_RIDS ((NTSTATUS)0xC00002A8L)
#define STATUS_DS_INCORRECT_ROLE_OWNER ((NTSTATUS)0xC00002A9L)
#define STATUS_DS_RIDMGR_INIT_ERROR ((NTSTATUS)0xC00002AAL)
#define STATUS_DS_OBJ_CLASS_VIOLATION ((NTSTATUS)0xC00002ABL)
#define STATUS_DS_CANT_ON_NON_LEAF ((NTSTATUS)0xC00002ACL)
#define STATUS_DS_CANT_ON_RDN ((NTSTATUS)0xC00002ADL)
#define STATUS_DS_CANT_MOD_OBJ_CLASS ((NTSTATUS)0xC00002AEL)
#define STATUS_DS_CROSS_DOM_MOVE_FAILED ((NTSTATUS)0xC00002AFL)
#define STATUS_DS_GC_NOT_AVAILABLE ((NTSTATUS)0xC00002B0L)
#define STATUS_DIRECTORY_SERVICE_REQUIRED ((NTSTATUS)0xC00002B1L)
#define STATUS_REPARSE_ATTRIBUTE_CONFLICT ((NTSTATUS)0xC00002B2L)
#define STATUS_CANT_ENABLE_DENY_ONLY ((NTSTATUS)0xC00002B3L)
#define STATUS_FLOAT_MULTIPLE_FAULTS ((NTSTATUS)0xC00002B4L)
#define STATUS_FLOAT_MULTIPLE_TRAPS ((NTSTATUS)0xC00002B5L)
#define STATUS_DEVICE_REMOVED ((NTSTATUS)0xC00002B6L)
#define STATUS_JOURNAL_DELETE_IN_PROGRESS ((NTSTATUS)0xC00002B7L)
#define STATUS_JOURNAL_NOT_ACTIVE ((NTSTATUS)0xC00002B8L)
#define STATUS_NOINTERFACE ((NTSTATUS)0xC00002B9L)
#define STATUS_DS_ADMIN_LIMIT_EXCEEDED ((NTSTATUS)0xC00002C1L)
#define STATUS_DRIVER_FAILED_SLEEP ((NTSTATUS)0xC00002C2L)
#define STATUS_MUTUAL_AUTHENTICATION_FAILED ((NTSTATUS)0xC00002C3L)
#define STATUS_CORRUPT_SYSTEM_FILE ((NTSTATUS)0xC00002C4L)
#define STATUS_DATATYPE_MISALIGNMENT_ERROR ((NTSTATUS)0xC00002C5L)
#define STATUS_WMI_READ_ONLY ((NTSTATUS)0xC00002C6L)
#define STATUS_WMI_SET_FAILURE ((NTSTATUS)0xC00002C7L)
#define STATUS_COMMITMENT_MINIMUM ((NTSTATUS)0xC00002C8L)
#define STATUS_REG_NAT_CONSUMPTION ((NTSTATUS)0xC00002C9L)
#define STATUS_TRANSPORT_FULL ((NTSTATUS)0xC00002CAL)
#define STATUS_DS_SAM_INIT_FAILURE ((NTSTATUS)0xC00002CBL)
#define STATUS_ONLY_IF_CONNECTED ((NTSTATUS)0xC00002CCL)
#define STATUS_DS_SENSITIVE_GROUP_VIOLATION ((NTSTATUS)0xC00002CDL)
#define STATUS_PNP_RESTART_ENUMERATION ((NTSTATUS)0xC00002CEL)
#define STATUS_JOURNAL_ENTRY_DELETED ((NTSTATUS)0xC00002CFL)
#define STATUS_DS_CANT_MOD_PRIMARYGROUPID ((NTSTATUS)0xC00002D0L)
#define STATUS_SYSTEM_IMAGE_BAD_SIGNATURE ((NTSTATUS)0xC00002D1L)
#define STATUS_PNP_REBOOT_REQUIRED ((NTSTATUS)0xC00002D2L)
#define STATUS_POWER_STATE_INVALID ((NTSTATUS)0xC00002D3L)
#define STATUS_DS_INVALID_GROUP_TYPE ((NTSTATUS)0xC00002D4L)
#define STATUS_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN ((NTSTATUS)0xC00002D5L)
#define STATUS_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN ((NTSTATUS)0xC00002D6L)
#define STATUS_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER ((NTSTATUS)0xC00002D7L)
#define STATUS_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER ((NTSTATUS)0xC00002D8L)
#define STATUS_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER ((NTSTATUS)0xC00002D9L)
#define STATUS_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER ((NTSTATUS)0xC00002DAL)
#define STATUS_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER ((NTSTATUS)0xC00002DBL)
#define STATUS_DS_HAVE_PRIMARY_MEMBERS ((NTSTATUS)0xC00002DCL)
#define STATUS_WMI_NOT_SUPPORTED ((NTSTATUS)0xC00002DDL)
#define STATUS_INSUFFICIENT_POWER ((NTSTATUS)0xC00002DEL)
#define STATUS_SAM_NEED_BOOTKEY_PASSWORD ((NTSTATUS)0xC00002DFL)
#define STATUS_SAM_NEED_BOOTKEY_FLOPPY ((NTSTATUS)0xC00002E0L)
#define STATUS_DS_CANT_START ((NTSTATUS)0xC00002E1L)
#define STATUS_DS_INIT_FAILURE ((NTSTATUS)0xC00002E2L)
#define STATUS_SAM_INIT_FAILURE ((NTSTATUS)0xC00002E3L)
#define STATUS_DS_GC_REQUIRED ((NTSTATUS)0xC00002E4L)
#define STATUS_DS_LOCAL_MEMBER_OF_LOCAL_ONLY ((NTSTATUS)0xC00002E5L)
#define STATUS_DS_NO_FPO_IN_UNIVERSAL_GROUPS ((NTSTATUS)0xC00002E6L)
#define STATUS_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED ((NTSTATUS)0xC00002E7L)
#define STATUS_MULTIPLE_FAULT_VIOLATION ((NTSTATUS)0xC00002E8L)
#define STATUS_CURRENT_DOMAIN_NOT_ALLOWED ((NTSTATUS)0xC00002E9L)
#define STATUS_CANNOT_MAKE ((NTSTATUS)0xC00002EAL)
#define STATUS_SYSTEM_SHUTDOWN ((NTSTATUS)0xC00002EBL)
#define STATUS_DS_INIT_FAILURE_CONSOLE ((NTSTATUS)0xC00002ECL)
#define STATUS_DS_SAM_INIT_FAILURE_CONSOLE ((NTSTATUS)0xC00002EDL)
#define STATUS_UNFINISHED_CONTEXT_DELETED ((NTSTATUS)0xC00002EEL)
#define STATUS_NO_TGT_REPLY ((NTSTATUS)0xC00002EFL)
#define STATUS_OBJECTID_NOT_FOUND ((NTSTATUS)0xC00002F0L)
#define STATUS_NO_IP_ADDRESSES ((NTSTATUS)0xC00002F1L)
#define STATUS_WRONG_CREDENTIAL_HANDLE ((NTSTATUS)0xC00002F2L)
#define STATUS_CRYPTO_SYSTEM_INVALID ((NTSTATUS)0xC00002F3L)
#define STATUS_MAX_REFERRALS_EXCEEDED ((NTSTATUS)0xC00002F4L)
#define STATUS_MUST_BE_KDC ((NTSTATUS)0xC00002F5L)
#define STATUS_STRONG_CRYPTO_NOT_SUPPORTED ((NTSTATUS)0xC00002F6L)
#define STATUS_TOO_MANY_PRINCIPALS ((NTSTATUS)0xC00002F7L)
#define STATUS_NO_PA_DATA ((NTSTATUS)0xC00002F8L)
#define STATUS_PKINIT_NAME_MISMATCH ((NTSTATUS)0xC00002F9L)
#define STATUS_SMARTCARD_LOGON_REQUIRED ((NTSTATUS)0xC00002FAL)
#define STATUS_KDC_INVALID_REQUEST ((NTSTATUS)0xC00002FBL)
#define STATUS_KDC_UNABLE_TO_REFER ((NTSTATUS)0xC00002FCL)
#define STATUS_KDC_UNKNOWN_ETYPE ((NTSTATUS)0xC00002FDL)
#define STATUS_SHUTDOWN_IN_PROGRESS ((NTSTATUS)0xC00002FEL)
#define STATUS_SERVER_SHUTDOWN_IN_PROGRESS ((NTSTATUS)0xC00002FFL)
#define STATUS_NOT_SUPPORTED_ON_SBS ((NTSTATUS)0xC0000300L)
#define STATUS_WMI_GUID_DISCONNECTED ((NTSTATUS)0xC0000301L)
#define STATUS_WMI_ALREADY_DISABLED ((NTSTATUS)0xC0000302L)
#define STATUS_WMI_ALREADY_ENABLED ((NTSTATUS)0xC0000303L)
#define STATUS_MFT_TOO_FRAGMENTED ((NTSTATUS)0xC0000304L)
#define STATUS_COPY_PROTECTION_FAILURE ((NTSTATUS)0xC0000305L)
#define STATUS_CSS_AUTHENTICATION_FAILURE ((NTSTATUS)0xC0000306L)
#define STATUS_CSS_KEY_NOT_PRESENT ((NTSTATUS)0xC0000307L)
#define STATUS_CSS_KEY_NOT_ESTABLISHED ((NTSTATUS)0xC0000308L)
#define STATUS_CSS_SCRAMBLED_SECTOR ((NTSTATUS)0xC0000309L)
#define STATUS_CSS_REGION_MISMATCH ((NTSTATUS)0xC000030AL)
#define STATUS_CSS_RESETS_EXHAUSTED ((NTSTATUS)0xC000030BL)
#define STATUS_PKINIT_FAILURE ((NTSTATUS)0xC0000320L)
#define STATUS_SMARTCARD_SUBSYSTEM_FAILURE ((NTSTATUS)0xC0000321L)
#define STATUS_NO_KERB_KEY ((NTSTATUS)0xC0000322L)
#define STATUS_HOST_DOWN ((NTSTATUS)0xC0000350L)
#define STATUS_UNSUPPORTED_PREAUTH ((NTSTATUS)0xC0000351L)
#define STATUS_EFS_ALG_BLOB_TOO_BIG ((NTSTATUS)0xC0000352L)
#define STATUS_PORT_NOT_SET ((NTSTATUS)0xC0000353L)
#define STATUS_DEBUGGER_INACTIVE ((NTSTATUS)0xC0000354L)
#define STATUS_DS_VERSION_CHECK_FAILURE ((NTSTATUS)0xC0000355L)
#define STATUS_AUDITING_DISABLED ((NTSTATUS)0xC0000356L)
#define STATUS_PRENT4_MACHINE_ACCOUNT ((NTSTATUS)0xC0000357L)
#define STATUS_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER ((NTSTATUS)0xC0000358L)
#define STATUS_INVALID_IMAGE_WIN_32 ((NTSTATUS)0xC0000359L)
#define STATUS_INVALID_IMAGE_WIN_64 ((NTSTATUS)0xC000035AL)
#define STATUS_BAD_BINDINGS ((NTSTATUS)0xC000035BL)
#define STATUS_NETWORK_SESSION_EXPIRED ((NTSTATUS)0xC000035CL)
#define STATUS_APPHELP_BLOCK ((NTSTATUS)0xC000035DL)
#define STATUS_ALL_SIDS_FILTERED ((NTSTATUS)0xC000035EL)
#define STATUS_NOT_SAFE_MODE_DRIVER ((NTSTATUS)0xC000035FL)
#define STATUS_ACCESS_DISABLED_BY_POLICY_DEFAULT ((NTSTATUS)0xC0000361L)
#define STATUS_ACCESS_DISABLED_BY_POLICY_PATH ((NTSTATUS)0xC0000362L)
#define STATUS_ACCESS_DISABLED_BY_POLICY_PUBLISHER ((NTSTATUS)0xC0000363L)
#define STATUS_ACCESS_DISABLED_BY_POLICY_OTHER ((NTSTATUS)0xC0000364L)
#define STATUS_FAILED_DRIVER_ENTRY ((NTSTATUS)0xC0000365L)
#define STATUS_DEVICE_ENUMERATION_ERROR ((NTSTATUS)0xC0000366L)
#define STATUS_WAIT_FOR_OPLOCK ((NTSTATUS)0x00000367L)
#define STATUS_MOUNT_POINT_NOT_RESOLVED ((NTSTATUS)0xC0000368L)
#define STATUS_INVALID_DEVICE_OBJECT_PARAMETER ((NTSTATUS)0xC0000369L)
#define STATUS_MCA_OCCURED ((NTSTATUS)0xC000036AL)
#define STATUS_DRIVER_BLOCKED_CRITICAL ((NTSTATUS)0xC000036BL)
#define STATUS_DRIVER_BLOCKED ((NTSTATUS)0xC000036CL)
#define STATUS_DRIVER_DATABASE_ERROR ((NTSTATUS)0xC000036DL)
#define STATUS_SYSTEM_HIVE_TOO_LARGE ((NTSTATUS)0xC000036EL)
#define STATUS_INVALID_IMPORT_OF_NON_DLL ((NTSTATUS)0xC000036FL)
#define STATUS_DS_SHUTTING_DOWN ((NTSTATUS)0x40000370L)
#define STATUS_SMARTCARD_WRONG_PIN ((NTSTATUS)0xC0000380L)
#define STATUS_SMARTCARD_CARD_BLOCKED ((NTSTATUS)0xC0000381L)
#define STATUS_SMARTCARD_CARD_NOT_AUTHENTICATED ((NTSTATUS)0xC0000382L)
#define STATUS_SMARTCARD_NO_CARD ((NTSTATUS)0xC0000383L)
#define STATUS_SMARTCARD_NO_KEY_CONTAINER ((NTSTATUS)0xC0000384L)
#define STATUS_SMARTCARD_NO_CERTIFICATE ((NTSTATUS)0xC0000385L)
#define STATUS_SMARTCARD_NO_KEYSET ((NTSTATUS)0xC0000386L)
#define STATUS_SMARTCARD_IO_ERROR ((NTSTATUS)0xC0000387L)
#define STATUS_DOWNGRADE_DETECTED ((NTSTATUS)0xC0000388L)
#define STATUS_SMARTCARD_CERT_REVOKED ((NTSTATUS)0xC0000389L)
#define STATUS_ISSUING_CA_UNTRUSTED ((NTSTATUS)0xC000038AL)
#define STATUS_REVOCATION_OFFLINE_C ((NTSTATUS)0xC000038BL)
#define STATUS_PKINIT_CLIENT_FAILURE ((NTSTATUS)0xC000038CL)
#define STATUS_SMARTCARD_CERT_EXPIRED ((NTSTATUS)0xC000038DL)
#define STATUS_DRIVER_FAILED_PRIOR_UNLOAD ((NTSTATUS)0xC000038EL)
#define STATUS_WOW_ASSERTION ((NTSTATUS)0xC0009898L)
#define RPC_NT_INVALID_STRING_BINDING ((NTSTATUS)0xC0020001L)
#define RPC_NT_WRONG_KIND_OF_BINDING ((NTSTATUS)0xC0020002L)
#define RPC_NT_INVALID_BINDING ((NTSTATUS)0xC0020003L)
#define RPC_NT_PROTSEQ_NOT_SUPPORTED ((NTSTATUS)0xC0020004L)
#define RPC_NT_INVALID_RPC_PROTSEQ ((NTSTATUS)0xC0020005L)
#define RPC_NT_INVALID_STRING_UUID ((NTSTATUS)0xC0020006L)
#define RPC_NT_INVALID_ENDPOINT_FORMAT ((NTSTATUS)0xC0020007L)
#define RPC_NT_INVALID_NET_ADDR ((NTSTATUS)0xC0020008L)
#define RPC_NT_NO_ENDPOINT_FOUND ((NTSTATUS)0xC0020009L)
#define RPC_NT_INVALID_TIMEOUT ((NTSTATUS)0xC002000AL)
#define RPC_NT_OBJECT_NOT_FOUND ((NTSTATUS)0xC002000BL)
#define RPC_NT_ALREADY_REGISTERED ((NTSTATUS)0xC002000CL)
#define RPC_NT_TYPE_ALREADY_REGISTERED ((NTSTATUS)0xC002000DL)
#define RPC_NT_ALREADY_LISTENING ((NTSTATUS)0xC002000EL)
#define RPC_NT_NO_PROTSEQS_REGISTERED ((NTSTATUS)0xC002000FL)
#define RPC_NT_NOT_LISTENING ((NTSTATUS)0xC0020010L)
#define RPC_NT_UNKNOWN_MGR_TYPE ((NTSTATUS)0xC0020011L)
#define RPC_NT_UNKNOWN_IF ((NTSTATUS)0xC0020012L)
#define RPC_NT_NO_BINDINGS ((NTSTATUS)0xC0020013L)
#define RPC_NT_NO_PROTSEQS ((NTSTATUS)0xC0020014L)
#define RPC_NT_CANT_CREATE_ENDPOINT ((NTSTATUS)0xC0020015L)
#define RPC_NT_OUT_OF_RESOURCES ((NTSTATUS)0xC0020016L)
#define RPC_NT_SERVER_UNAVAILABLE ((NTSTATUS)0xC0020017L)
#define RPC_NT_SERVER_TOO_BUSY ((NTSTATUS)0xC0020018L)
#define RPC_NT_INVALID_NETWORK_OPTIONS ((NTSTATUS)0xC0020019L)
#define RPC_NT_NO_CALL_ACTIVE ((NTSTATUS)0xC002001AL)
#define RPC_NT_CALL_FAILED ((NTSTATUS)0xC002001BL)
#define RPC_NT_CALL_FAILED_DNE ((NTSTATUS)0xC002001CL)
#define RPC_NT_PROTOCOL_ERROR ((NTSTATUS)0xC002001DL)
#define RPC_NT_UNSUPPORTED_TRANS_SYN ((NTSTATUS)0xC002001FL)
#define RPC_NT_UNSUPPORTED_TYPE ((NTSTATUS)0xC0020021L)
#define RPC_NT_INVALID_TAG ((NTSTATUS)0xC0020022L)
#define RPC_NT_INVALID_BOUND ((NTSTATUS)0xC0020023L)
#define RPC_NT_NO_ENTRY_NAME ((NTSTATUS)0xC0020024L)
#define RPC_NT_INVALID_NAME_SYNTAX ((NTSTATUS)0xC0020025L)
#define RPC_NT_UNSUPPORTED_NAME_SYNTAX ((NTSTATUS)0xC0020026L)
#define RPC_NT_UUID_NO_ADDRESS ((NTSTATUS)0xC0020028L)
#define RPC_NT_DUPLICATE_ENDPOINT ((NTSTATUS)0xC0020029L)
#define RPC_NT_UNKNOWN_AUTHN_TYPE ((NTSTATUS)0xC002002AL)
#define RPC_NT_MAX_CALLS_TOO_SMALL ((NTSTATUS)0xC002002BL)
#define RPC_NT_STRING_TOO_LONG ((NTSTATUS)0xC002002CL)
#define RPC_NT_PROTSEQ_NOT_FOUND ((NTSTATUS)0xC002002DL)
#define RPC_NT_PROCNUM_OUT_OF_RANGE ((NTSTATUS)0xC002002EL)
#define RPC_NT_BINDING_HAS_NO_AUTH ((NTSTATUS)0xC002002FL)
#define RPC_NT_UNKNOWN_AUTHN_SERVICE ((NTSTATUS)0xC0020030L)
#define RPC_NT_UNKNOWN_AUTHN_LEVEL ((NTSTATUS)0xC0020031L)
#define RPC_NT_INVALID_AUTH_IDENTITY ((NTSTATUS)0xC0020032L)
#define RPC_NT_UNKNOWN_AUTHZ_SERVICE ((NTSTATUS)0xC0020033L)
#define EPT_NT_INVALID_ENTRY ((NTSTATUS)0xC0020034L)
#define EPT_NT_CANT_PERFORM_OP ((NTSTATUS)0xC0020035L)
#define EPT_NT_NOT_REGISTERED ((NTSTATUS)0xC0020036L)
#define RPC_NT_NOTHING_TO_EXPORT ((NTSTATUS)0xC0020037L)
#define RPC_NT_INCOMPLETE_NAME ((NTSTATUS)0xC0020038L)
#define RPC_NT_INVALID_VERS_OPTION ((NTSTATUS)0xC0020039L)
#define RPC_NT_NO_MORE_MEMBERS ((NTSTATUS)0xC002003AL)
#define RPC_NT_NOT_ALL_OBJS_UNEXPORTED ((NTSTATUS)0xC002003BL)
#define RPC_NT_INTERFACE_NOT_FOUND ((NTSTATUS)0xC002003CL)
#define RPC_NT_ENTRY_ALREADY_EXISTS ((NTSTATUS)0xC002003DL)
#define RPC_NT_ENTRY_NOT_FOUND ((NTSTATUS)0xC002003EL)
#define RPC_NT_NAME_SERVICE_UNAVAILABLE ((NTSTATUS)0xC002003FL)
#define RPC_NT_INVALID_NAF_ID ((NTSTATUS)0xC0020040L)
#define RPC_NT_CANNOT_SUPPORT ((NTSTATUS)0xC0020041L)
#define RPC_NT_NO_CONTEXT_AVAILABLE ((NTSTATUS)0xC0020042L)
#define RPC_NT_INTERNAL_ERROR ((NTSTATUS)0xC0020043L)
#define RPC_NT_ZERO_DIVIDE ((NTSTATUS)0xC0020044L)
#define RPC_NT_ADDRESS_ERROR ((NTSTATUS)0xC0020045L)
#define RPC_NT_FP_DIV_ZERO ((NTSTATUS)0xC0020046L)
#define RPC_NT_FP_UNDERFLOW ((NTSTATUS)0xC0020047L)
#define RPC_NT_FP_OVERFLOW ((NTSTATUS)0xC0020048L)
#define RPC_NT_NO_MORE_ENTRIES ((NTSTATUS)0xC0030001L)
#define RPC_NT_SS_CHAR_TRANS_OPEN_FAIL ((NTSTATUS)0xC0030002L)
#define RPC_NT_SS_CHAR_TRANS_SHORT_FILE ((NTSTATUS)0xC0030003L)
#define RPC_NT_SS_IN_NULL_CONTEXT ((NTSTATUS)0xC0030004L)
#define RPC_NT_SS_CONTEXT_MISMATCH ((NTSTATUS)0xC0030005L)
#define RPC_NT_SS_CONTEXT_DAMAGED ((NTSTATUS)0xC0030006L)
#define RPC_NT_SS_HANDLES_MISMATCH ((NTSTATUS)0xC0030007L)
#define RPC_NT_SS_CANNOT_GET_CALL_HANDLE ((NTSTATUS)0xC0030008L)
#define RPC_NT_NULL_REF_POINTER ((NTSTATUS)0xC0030009L)
#define RPC_NT_ENUM_VALUE_OUT_OF_RANGE ((NTSTATUS)0xC003000AL)
#define RPC_NT_BYTE_COUNT_TOO_SMALL ((NTSTATUS)0xC003000BL)
#define RPC_NT_BAD_STUB_DATA ((NTSTATUS)0xC003000CL)
#define RPC_NT_CALL_IN_PROGRESS ((NTSTATUS)0xC0020049L)
#define RPC_NT_NO_MORE_BINDINGS ((NTSTATUS)0xC002004AL)
#define RPC_NT_GROUP_MEMBER_NOT_FOUND ((NTSTATUS)0xC002004BL)
#define EPT_NT_CANT_CREATE ((NTSTATUS)0xC002004CL)
#define RPC_NT_INVALID_OBJECT ((NTSTATUS)0xC002004DL)
#define RPC_NT_NO_INTERFACES ((NTSTATUS)0xC002004FL)
#define RPC_NT_CALL_CANCELLED ((NTSTATUS)0xC0020050L)
#define RPC_NT_BINDING_INCOMPLETE ((NTSTATUS)0xC0020051L)
#define RPC_NT_COMM_FAILURE ((NTSTATUS)0xC0020052L)
#define RPC_NT_UNSUPPORTED_AUTHN_LEVEL ((NTSTATUS)0xC0020053L)
#define RPC_NT_NO_PRINC_NAME ((NTSTATUS)0xC0020054L)
#define RPC_NT_NOT_RPC_ERROR ((NTSTATUS)0xC0020055L)
#define RPC_NT_UUID_LOCAL_ONLY ((NTSTATUS)0x40020056L)
#define RPC_NT_SEC_PKG_ERROR ((NTSTATUS)0xC0020057L)
#define RPC_NT_NOT_CANCELLED ((NTSTATUS)0xC0020058L)
#define RPC_NT_INVALID_ES_ACTION ((NTSTATUS)0xC0030059L)
#define RPC_NT_WRONG_ES_VERSION ((NTSTATUS)0xC003005AL)
#define RPC_NT_WRONG_STUB_VERSION ((NTSTATUS)0xC003005BL)
#define RPC_NT_INVALID_PIPE_OBJECT ((NTSTATUS)0xC003005CL)
#define RPC_NT_INVALID_PIPE_OPERATION ((NTSTATUS)0xC003005DL)
#define RPC_NT_WRONG_PIPE_VERSION ((NTSTATUS)0xC003005EL)
#define RPC_NT_PIPE_CLOSED ((NTSTATUS)0xC003005FL)
#define RPC_NT_PIPE_DISCIPLINE_ERROR ((NTSTATUS)0xC0030060L)
#define RPC_NT_PIPE_EMPTY ((NTSTATUS)0xC0030061L)
#define RPC_NT_INVALID_ASYNC_HANDLE ((NTSTATUS)0xC0020062L)
#define RPC_NT_INVALID_ASYNC_CALL ((NTSTATUS)0xC0020063L)
#define RPC_NT_SEND_INCOMPLETE ((NTSTATUS)0x400200AFL)
#define STATUS_ACPI_INVALID_OPCODE ((NTSTATUS)0xC0140001L)
#define STATUS_ACPI_STACK_OVERFLOW ((NTSTATUS)0xC0140002L)
#define STATUS_ACPI_ASSERT_FAILED ((NTSTATUS)0xC0140003L)
#define STATUS_ACPI_INVALID_INDEX ((NTSTATUS)0xC0140004L)
#define STATUS_ACPI_INVALID_ARGUMENT ((NTSTATUS)0xC0140005L)
#define STATUS_ACPI_FATAL ((NTSTATUS)0xC0140006L)
#define STATUS_ACPI_INVALID_SUPERNAME ((NTSTATUS)0xC0140007L)
#define STATUS_ACPI_INVALID_ARGTYPE ((NTSTATUS)0xC0140008L)
#define STATUS_ACPI_INVALID_OBJTYPE ((NTSTATUS)0xC0140009L)
#define STATUS_ACPI_INVALID_TARGETTYPE ((NTSTATUS)0xC014000AL)
#define STATUS_ACPI_INCORRECT_ARGUMENT_COUNT ((NTSTATUS)0xC014000BL)
#define STATUS_ACPI_ADDRESS_NOT_MAPPED ((NTSTATUS)0xC014000CL)
#define STATUS_ACPI_INVALID_EVENTTYPE ((NTSTATUS)0xC014000DL)
#define STATUS_ACPI_HANDLER_COLLISION ((NTSTATUS)0xC014000EL)
#define STATUS_ACPI_INVALID_DATA ((NTSTATUS)0xC014000FL)
#define STATUS_ACPI_INVALID_REGION ((NTSTATUS)0xC0140010L)
#define STATUS_ACPI_INVALID_ACCESS_SIZE ((NTSTATUS)0xC0140011L)
#define STATUS_ACPI_ACQUIRE_GLOBAL_LOCK ((NTSTATUS)0xC0140012L)
#define STATUS_ACPI_ALREADY_INITIALIZED ((NTSTATUS)0xC0140013L)
#define STATUS_ACPI_NOT_INITIALIZED ((NTSTATUS)0xC0140014L)
#define STATUS_ACPI_INVALID_MUTEX_LEVEL ((NTSTATUS)0xC0140015L)
#define STATUS_ACPI_MUTEX_NOT_OWNED ((NTSTATUS)0xC0140016L)
#define STATUS_ACPI_MUTEX_NOT_OWNER ((NTSTATUS)0xC0140017L)
#define STATUS_ACPI_RS_ACCESS ((NTSTATUS)0xC0140018L)
#define STATUS_ACPI_INVALID_TABLE ((NTSTATUS)0xC0140019L)
#define STATUS_ACPI_REG_HANDLER_FAILED ((NTSTATUS)0xC0140020L)
#define STATUS_ACPI_POWER_REQUEST_FAILED ((NTSTATUS)0xC0140021L)
#define STATUS_CTX_WINSTATION_NAME_INVALID ((NTSTATUS)0xC00A0001L)
#define STATUS_CTX_INVALID_PD ((NTSTATUS)0xC00A0002L)
#define STATUS_CTX_PD_NOT_FOUND ((NTSTATUS)0xC00A0003L)
#define STATUS_CTX_CDM_CONNECT ((NTSTATUS)0x400A0004L)
#define STATUS_CTX_CDM_DISCONNECT ((NTSTATUS)0x400A0005L)
#define STATUS_CTX_CLOSE_PENDING ((NTSTATUS)0xC00A0006L)
#define STATUS_CTX_NO_OUTBUF ((NTSTATUS)0xC00A0007L)
#define STATUS_CTX_MODEM_INF_NOT_FOUND ((NTSTATUS)0xC00A0008L)
#define STATUS_CTX_INVALID_MODEMNAME ((NTSTATUS)0xC00A0009L)
#define STATUS_CTX_RESPONSE_ERROR ((NTSTATUS)0xC00A000AL)
#define STATUS_CTX_MODEM_RESPONSE_TIMEOUT ((NTSTATUS)0xC00A000BL)
#define STATUS_CTX_MODEM_RESPONSE_NO_CARRIER ((NTSTATUS)0xC00A000CL)
#define STATUS_CTX_MODEM_RESPONSE_NO_DIALTONE ((NTSTATUS)0xC00A000DL)
#define STATUS_CTX_MODEM_RESPONSE_BUSY ((NTSTATUS)0xC00A000EL)
#define STATUS_CTX_MODEM_RESPONSE_VOICE ((NTSTATUS)0xC00A000FL)
#define STATUS_CTX_TD_ERROR ((NTSTATUS)0xC00A0010L)
#define STATUS_CTX_LICENSE_CLIENT_INVALID ((NTSTATUS)0xC00A0012L)
#define STATUS_CTX_LICENSE_NOT_AVAILABLE ((NTSTATUS)0xC00A0013L)
#define STATUS_CTX_LICENSE_EXPIRED ((NTSTATUS)0xC00A0014L)
#define STATUS_CTX_WINSTATION_NOT_FOUND ((NTSTATUS)0xC00A0015L)
#define STATUS_CTX_WINSTATION_NAME_COLLISION ((NTSTATUS)0xC00A0016L)
#define STATUS_CTX_WINSTATION_BUSY ((NTSTATUS)0xC00A0017L)
#define STATUS_CTX_BAD_VIDEO_MODE ((NTSTATUS)0xC00A0018L)
#define STATUS_CTX_GRAPHICS_INVALID ((NTSTATUS)0xC00A0022L)
#define STATUS_CTX_NOT_CONSOLE ((NTSTATUS)0xC00A0024L)
#define STATUS_CTX_CLIENT_QUERY_TIMEOUT ((NTSTATUS)0xC00A0026L)
#define STATUS_CTX_CONSOLE_DISCONNECT ((NTSTATUS)0xC00A0027L)
#define STATUS_CTX_CONSOLE_CONNECT ((NTSTATUS)0xC00A0028L)
#define STATUS_CTX_SHADOW_DENIED ((NTSTATUS)0xC00A002AL)
#define STATUS_CTX_WINSTATION_ACCESS_DENIED ((NTSTATUS)0xC00A002BL)
#define STATUS_CTX_INVALID_WD ((NTSTATUS)0xC00A002EL)
#define STATUS_CTX_WD_NOT_FOUND ((NTSTATUS)0xC00A002FL)
#define STATUS_CTX_SHADOW_INVALID ((NTSTATUS)0xC00A0030L)
#define STATUS_CTX_SHADOW_DISABLED ((NTSTATUS)0xC00A0031L)
#define STATUS_RDP_PROTOCOL_ERROR ((NTSTATUS)0xC00A0032L)
#define STATUS_CTX_CLIENT_LICENSE_NOT_SET ((NTSTATUS)0xC00A0033L)
#define STATUS_CTX_CLIENT_LICENSE_IN_USE ((NTSTATUS)0xC00A0034L)
#define STATUS_CTX_SHADOW_ENDED_BY_MODE_CHANGE ((NTSTATUS)0xC00A0035L)
#define STATUS_CTX_SHADOW_NOT_RUNNING ((NTSTATUS)0xC00A0036L)
#define STATUS_PNP_BAD_MPS_TABLE ((NTSTATUS)0xC0040035L)
#define STATUS_PNP_TRANSLATION_FAILED ((NTSTATUS)0xC0040036L)
#define STATUS_PNP_IRQ_TRANSLATION_FAILED ((NTSTATUS)0xC0040037L)
#define STATUS_SXS_SECTION_NOT_FOUND ((NTSTATUS)0xC0150001L)
#define STATUS_SXS_CANT_GEN_ACTCTX ((NTSTATUS)0xC0150002L)
#define STATUS_SXS_INVALID_ACTCTXDATA_FORMAT ((NTSTATUS)0xC0150003L)
#define STATUS_SXS_ASSEMBLY_NOT_FOUND ((NTSTATUS)0xC0150004L)
#define STATUS_SXS_MANIFEST_FORMAT_ERROR ((NTSTATUS)0xC0150005L)
#define STATUS_SXS_MANIFEST_PARSE_ERROR ((NTSTATUS)0xC0150006L)
#define STATUS_SXS_ACTIVATION_CONTEXT_DISABLED ((NTSTATUS)0xC0150007L)
#define STATUS_SXS_KEY_NOT_FOUND ((NTSTATUS)0xC0150008L)
#define STATUS_SXS_VERSION_CONFLICT ((NTSTATUS)0xC0150009L)
#define STATUS_SXS_WRONG_SECTION_TYPE ((NTSTATUS)0xC015000AL)
#define STATUS_SXS_THREAD_QUERIES_DISABLED ((NTSTATUS)0xC015000BL)
#define STATUS_SXS_ASSEMBLY_MISSING ((NTSTATUS)0xC015000CL)
#define STATUS_SXS_RELEASE_ACTIVATION_CONTEXT ((NTSTATUS)0x4015000DL)
#define STATUS_SXS_PROCESS_DEFAULT_ALREADY_SET ((NTSTATUS)0xC015000EL)
#define STATUS_SXS_EARLY_DEACTIVATION ((NTSTATUS)0xC015000FL)
#define STATUS_SXS_INVALID_DEACTIVATION ((NTSTATUS)0xC0150010L)
#define STATUS_SXS_MULTIPLE_DEACTIVATION ((NTSTATUS)0xC0150011L)
#define STATUS_SXS_SYSTEM_DEFAULT_ACTIVATION_CONTEXT_EMPTY ((NTSTATUS)0xC0150012L)
#define STATUS_SXS_PROCESS_TERMINATION_REQUESTED ((NTSTATUS)0xC0150013L)
#define STATUS_CLUSTER_INVALID_NODE ((NTSTATUS)0xC0130001L)
#define STATUS_CLUSTER_NODE_EXISTS ((NTSTATUS)0xC0130002L)
#define STATUS_CLUSTER_JOIN_IN_PROGRESS ((NTSTATUS)0xC0130003L)
#define STATUS_CLUSTER_NODE_NOT_FOUND ((NTSTATUS)0xC0130004L)
#define STATUS_CLUSTER_LOCAL_NODE_NOT_FOUND ((NTSTATUS)0xC0130005L)
#define STATUS_CLUSTER_NETWORK_EXISTS ((NTSTATUS)0xC0130006L)
#define STATUS_CLUSTER_NETWORK_NOT_FOUND ((NTSTATUS)0xC0130007L)
#define STATUS_CLUSTER_NETINTERFACE_EXISTS ((NTSTATUS)0xC0130008L)
#define STATUS_CLUSTER_NETINTERFACE_NOT_FOUND ((NTSTATUS)0xC0130009L)
#define STATUS_CLUSTER_INVALID_REQUEST ((NTSTATUS)0xC013000AL)
#define STATUS_CLUSTER_INVALID_NETWORK_PROVIDER ((NTSTATUS)0xC013000BL)
#define STATUS_CLUSTER_NODE_DOWN ((NTSTATUS)0xC013000CL)
#define STATUS_CLUSTER_NODE_UNREACHABLE ((NTSTATUS)0xC013000DL)
#define STATUS_CLUSTER_NODE_NOT_MEMBER ((NTSTATUS)0xC013000EL)
#define STATUS_CLUSTER_JOIN_NOT_IN_PROGRESS ((NTSTATUS)0xC013000FL)
#define STATUS_CLUSTER_INVALID_NETWORK ((NTSTATUS)0xC0130010L)
#define STATUS_CLUSTER_NO_NET_ADAPTERS ((NTSTATUS)0xC0130011L)
#define STATUS_CLUSTER_NODE_UP ((NTSTATUS)0xC0130012L)
#define STATUS_CLUSTER_NODE_PAUSED ((NTSTATUS)0xC0130013L)
#define STATUS_CLUSTER_NODE_NOT_PAUSED ((NTSTATUS)0xC0130014L)
#define STATUS_CLUSTER_NO_SECURITY_CONTEXT ((NTSTATUS)0xC0130015L)
#define STATUS_CLUSTER_NETWORK_NOT_INTERNAL ((NTSTATUS)0xC0130016L)
#define STATUS_CLUSTER_POISONED ((NTSTATUS)0xC0130017L)

#ifdef __cplusplus
}
#endif

#endif

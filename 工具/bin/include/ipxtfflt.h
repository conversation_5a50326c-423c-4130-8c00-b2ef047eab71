/*
 * ipxtfflt.h - Routing and Remote Access Services
 *
 * THIS SOFTWARE IS NOT COPYRIGHTED
 *
 * This source code is offered for use in the public domain.  You may use,
 * modify or distribute it freely.
 *
 * This code is distributed in the hope that it will be useful but
 * WITHOUT ANY WARRANTY.  ALL WARRANTIES, EXPRESS OR IMPLIED ARE HEREBY
 * DISCLAIMED.  This includes but is not limited to warranties of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *
 */
#ifndef _IPXTFFLT_H
#define _IPXTFFLT_H
#if __GNUC__ >= 3
#pragma GCC system_header
#endif

#ifdef __cplusplus
extern "C" {
#endif

/*--- Router Management Reference - Router Information Structures - IPX Information Structures */
#if 0
typedef struct {
	???
} IPX_TRAFFIC_FILTER_INFO,*PIPX_TRAFFIC_FILTER_INFO;
typedef struct {
	???
} IPX_TRAFFIC_FILTER_GLOBAL_INFO,*PIPX_TRAFFIC_FILTER_GLOBAL_INFO;
#endif

#ifdef __cplusplus
}
#endif
#endif

#ifndef _ERRORS_H
#define _ERRORS_H
#if __GNUC__ >=3
#pragma GCC system_header
#endif

#ifdef __cplusplus
extern "C" {
#endif

/*--- DirectShow Reference - Constants and GUIDs - Error and Success Codes */
#define VFW_S_NO_MORE_ITEMS 0x00040103
#define VFW_S_DUPLICATE_NAME 0x0004022D
#define VFW_S_STATE_INTERMEDIATE 0x00040237
#define VFW_S_PARTIAL_RENDER 0x00040242
#define VFW_S_SOME_DATA_IGNORED 0x00040245
#define VFW_S_CONNECTIONS_DEFERRED 0x00040246
#define VFW_S_RESOURCE_NOT_NEEDED 0x00040250
#define VFW_S_MEDIA_TYPE_IGNORED 0x00040254
#define VFW_S_VIDEO_NOT_RENDERED 0x00040257
#define VFW_S_AUDIO_NOT_RENDERED 0x00040258
#define VFW_S_RPZA 0x0004025A
#define VFW_S_ESTIMATED 0x00040260
#define VFW_S_RESERVED 0x00040263
#define VFW_S_STREAM_OFF 0x00040267
#define VFW_S_CANT_CUE 0x00040268
#define VFW_S_NOPREVIEWPIN 0x0004027E
#define VFW_S_DVD_NON_ONE_SEQUENTIAL 0x00040280
#define VFW_S_DVD_CHANNEL_CONTENTS_NOT_AVAILABLE 0x0004028C
#define VFW_S_DVD_NOT_ACCURATE 0x0004028D
#define VFW_E_INVALIDMEDIATYPE 0x80040200
#define VFW_E_INVALIDSUBTYPE 0x80040201
#define VFW_E_NEED_OWNER 0x80040202
#define VFW_E_ENUM_OUT_OF_SYNC 0x80040203
#define VFW_E_ALREADY_CONNECTED 0x80040204
#define VFW_E_FILTER_ACTIVE 0x80040205
#define VFW_E_NO_TYPES 0x80040206
#define VFW_E_NO_ACCEPTABLE_TYPES 0x80040207
#define VFW_E_INVALID_DIRECTION 0x80040208
#define VFW_E_NOT_CONNECTED 0x80040209
#define VFW_E_NO_ALLOCATOR 0x8004020A
#define VFW_E_RUNTIME_ERROR 0x8004020B
#define VFW_E_BUFFER_NOTSET 0x8004020C
#define VFW_E_BUFFER_OVERFLOW 0x8004020D
#define VFW_E_BADALIGN 0x8004020E
#define VFW_E_ALREADY_COMMITTED 0x8004020F
#define VFW_E_BUFFERS_OUTSTANDING 0x80040210
#define VFW_E_NOT_COMMITTED 0x80040211
#define VFW_E_SIZENOTSET 0x80040212
#define VFW_E_NO_CLOCK 0x80040213
#define VFW_E_NO_SINK 0x80040214
#define VFW_E_NO_INTERFACE 0x80040215
#define VFW_E_NOT_FOUND 0x80040216
#define VFW_E_CANNOT_CONNECT 0x80040217
#define VFW_E_CANNOT_RENDER 0x80040218
#define VFW_E_CHANGING_FORMAT 0x80040219
#define VFW_E_NO_COLOR_KEY_SET 0x8004021A
#define VFW_E_NOT_OVERLAY_CONNECTION 0x8004021B
#define VFW_E_NOT_SAMPLE_CONNECTION 0x8004021C
#define VFW_E_PALETTE_SET 0x8004021D
#define VFW_E_COLOR_KEY_SET 0x8004021E
#define VFW_E_NO_COLOR_KEY_FOUND 0x8004021F
#define VFW_E_NO_PALETTE_AVAILABLE 0x80040220
#define VFW_E_NO_DISPLAY_PALETTE 0x80040221
#define VFW_E_TOO_MANY_COLORS 0x80040222
#define VFW_E_STATE_CHANGED 0x80040223
#define VFW_E_NOT_STOPPED 0x80040224
#define VFW_E_NOT_PAUSED 0x80040225
#define VFW_E_NOT_RUNNING 0x80040226
#define VFW_E_WRONG_STATE 0x80040227
#define VFW_E_START_TIME_AFTER_END 0x80040228
#define VFW_E_INVALID_RECT 0x80040229
#define VFW_E_TYPE_NOT_ACCEPTED 0x8004022A
#define VFW_E_SAMPLE_REJECTED 0x8004022B
#define VFW_E_SAMPLE_REJECTED_EOS 0x8004022C
#define VFW_E_DUPLICATE_NAME 0x8004022D
#define VFW_E_TIMEOUT 0x8004022E
#define VFW_E_INVALID_FILE_FORMAT 0x8004022F
#define VFW_E_ENUM_OUT_OF_RANGE 0x80040230
#define VFW_E_CIRCULAR_GRAPH 0x80040231
#define VFW_E_NOT_ALLOWED_TO_SAVE 0x80040232
#define VFW_E_TIME_ALREADY_PASSED 0x80040233
#define VFW_E_ALREADY_CANCELLED 0x80040234
#define VFW_E_CORRUPT_GRAPH_FILE 0x80040235
#define VFW_E_ADVISE_ALREADY_SET 0x80040236
#define VFW_E_NO_MODEX_AVAILABLE 0x80040238
#define VFW_E_NO_ADVISE_SET 0x80040239
#define VFW_E_NO_FULLSCREEN 0x8004023A
#define VFW_E_IN_FULLSCREEN_MODE 0x8004023B
#define VFW_E_UNKNOWN_FILE_TYPE 0x80040240
#define VFW_E_CANNOT_LOAD_SOURCE_FILTER 0x80040241
#define VFW_E_FILE_TOO_SHORT 0x80040243
#define VFW_E_INVALID_FILE_VERSION 0x80040244
#define VFW_E_INVALID_CLSID 0x80040247
#define VFW_E_INVALID_MEDIA_TYPE 0x80040248
#define VFW_E_SAMPLE_TIME_NOT_SET 0x80040249
#define VFW_E_MEDIA_TIME_NOT_SET 0x80040251
#define VFW_E_NO_TIME_FORMAT_SET 0x80040252
#define VFW_E_MONO_AUDIO_HW 0x80040253
#define VFW_E_NO_DECOMPRESSOR 0x80040255
#define VFW_E_NO_AUDIO_HARDWARE 0x80040256
#define VFW_E_RPZA 0x80040259
#define VFW_E_PROCESSOR_NOT_SUITABLE 0x8004025B
#define VFW_E_UNSUPPORTED_AUDIO 0x8004025C
#define VFW_E_UNSUPPORTED_VIDEO 0x8004025D
#define VFW_E_MPEG_NOT_CONSTRAINED 0x8004025E
#define VFW_E_NOT_IN_GRAPH 0x8004025F
#define VFW_E_NO_TIME_FORMAT 0x80040261
#define VFW_E_READ_ONLY 0x80040262
#define VFW_E_BUFFER_UNDERFLOW 0x80040264
#define VFW_E_UNSUPPORTED_STREAM 0x80040265
#define VFW_E_NO_TRANSPORT 0x80040266
#define VFW_E_BAD_VIDEOCD 0x80040269
#define VFW_S_NO_STOP_TIME 0x80040270
#define VFW_E_OUT_OF_VIDEO_MEMORY 0x80040271
#define VFW_E_VP_NEGOTIATION_FAILED 0x80040272
#define VFW_E_DDRAW_CAPS_NOT_SUITABLE 0x80040273
#define VFW_E_NO_VP_HARDWARE 0x80040274
#define VFW_E_NO_CAPTURE_HARDWARE 0x80040275
#define VFW_E_DVD_OPERATION_INHIBITED 0x80040276
#define VFW_E_DVD_INVALIDDOMAIN 0x80040277
#define VFW_E_DVD_NO_BUTTON 0x80040278
#define VFW_E_DVD_GRAPHNOTREADY 0x80040279
#define VFW_E_DVD_RENDERFAIL 0x8004027A
#define VFW_E_DVD_DECNOTENOUGH 0x8004027B
#define VFW_E_DDRAW_VERSION_NOT_SUITABLE 0x8004027C
#define VFW_E_COPYPROT_FAILED 0x8004027D
#define VFW_E_TIME_EXPIRED 0x8004027F
#define VFW_E_DVD_WRONG_SPEED 0x80040281
#define VFW_E_DVD_MENU_DOES_NOT_EXIST 0x80040282
#define VFW_E_DVD_CMD_CANCELLED 0x80040283
#define VFW_E_DVD_STATE_WRONG_VERSION 0x80040284
#define VFW_E_DVD_STATE_CORRUPT 0x80040285
#define VFW_E_DVD_STATE_WRONG_DISC 0x80040286
#define VFW_E_DVD_INCOMPATIBLE_REGION 0x80040287
#define VFW_E_DVD_NO_ATTRIBUTES 0x80040288
#define VFW_E_DVD_NO_GOUP_PGC 0x80040289
#define VFW_E_DVD_LOW_PARENTAL_LEVEL 0x8004028A
#define VFW_E_DVD_NOT_IN_KARAOKE_MODE 0x8004028B
#define VFW_E_FRAME_STEP_UNSUPPORTED 0x8004028E
#define VFW_E_DVD_STREAM_DISABLED 0x8004028F
#define VFW_E_DVD_TITLE_UNKNOWN 0x80040290
#define VFW_E_DVD_INVALID_DISC 0x80040291
#define VFW_E_DVD_NO_RESUME_INFORMATION 0x80040292
#define VFW_E_PIN_ALREADY_BLOCKED_ON_THIS_THREAD 0x80040293
#define VFW_E_PIN_ALREADY_BLOCKED 0x80040294
#define VFW_E_CERTIFICATION_FAILURE 0x80040295
#define VFW_E_VMR_NOT_IN_MIXER_MODE 0x80040296
#define VFW_E_VMR_NO_AP_SUPPLIED 0x80040297
#define VFW_E_VMR_NO_DEINTERLACE_HW 0x80040298
#define VFW_E_VMR_NO_PROCAMP_HW 0x80040299
#define VFW_E_DVD_VMR9_INCOMPATIBLEDEC 0x8004029A
#define VFW_E_NO_COPP_HW 0x8004029B
#define VFW_E_BAD_KEY 0x800403F2
/*--- DirectShow Reference - Functions */
#define MAX_ERROR_TEXT_LEN 160
/*--- DirectShow Reference - Functions */
DWORD WINAPI AMGetErrorTextA(HRESULT,CHAR*,DWORD);
DWORD WINAPI AMGetErrorTextW(HRESULT,WCHAR*,DWORD);
#ifdef UNICODE
#define AMGetErrorText AMGetErrorTextW
#else
#define AMGetErrorText AMGetErrorTextA
#endif

#ifdef __cplusplus
}
#endif
#endif

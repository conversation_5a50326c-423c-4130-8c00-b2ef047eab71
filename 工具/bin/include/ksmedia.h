#ifndef _KSMEDIA_H
#define _KSMEDIA_H
#if __GNUC__ >= 3
#pragma GCC system_header
#endif

#ifdef __cplusplus
extern "C" {
#endif

/*--- DirectShow Reference - DirectShow Enumerated Types - CameraControlFlags Enumeration */
#define KSPROPERTY_CAMERACONTROL_FLAGS_AUTO 0x0001L
#define KSPROPERTY_CAMERACONTROL_FLAGS_MANUAL 0x0002L
#define KSPROPERTY_CAMERACONTROL_FLAGS_ABSOLUTE 0x0000L
#define KSPROPERTY_CAMERACONTROL_FLAGS_RELATIVE 0x0010L
/*--- DirectShow Reference - DirectShow Enumerated Types - VideoProcAmpFlags Enumeration */
#define KSPROPERTY_VIDEOPROCAMP_FLAGS_AUTO 0X0001L
#define KSPROPERTY_VIDEOPROCAMP_FLAGS_MANUAL 0X0002L

#ifdef __cplusplus
}
#endif
#endif

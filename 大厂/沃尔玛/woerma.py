"""
File Name: 获取_pxvid令牌 并请求公开数据
Author: 小木_.
Date Created: 2023-07-14
Last Modified: 2024-12-19
Version: 1.0
"""
import random
import requests
import hashlib
import time
import re
import base64

'''
如果出现请求错误，请更换请求的商品链接即可
如果更换后依然报错，有可能是网站进行了更新。
'''




# 这个是链接被base64加密了，此步骤只用于去敏，无其他作用
hosturl = 'https://www.walmart.com/'


class main:
    def md5_encrypt(self, string):
        '''
        'md5', 'sha1', 'sha224', 'sha256', 'sha384', 'sha512',
        'blake2b', 'blake2s',
        'sha3_224', 'sha3_256', 'sha3_384', 'sha3_512',
        'shake_128', 'shake_256'
        '''
        md5 = hashlib.md5()
        md5.update(string.encode('utf-8'))
        return md5.hexdigest()

    def Headers(self):
        # headers 过服务器分析验证 主要用于混淆服务器对你header分析做出的指纹验证
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9",
            f"F{self.md5_encrypt(str(time.time()))[:5]}": f"{self.md5_encrypt(str(random.randint(1, 10000)))}",
            "Sec-Ch-Ua": f"\"Not A(Brand\";v=\"{random.randint(70, 99)}\", \"Brave\";v=\"{random.randint(70, 120)}\", \"Chromium\";v=\"{random.randint(70, 120)}\"",
            "User-Agent": f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{random.randint(512, 538)}.{random.randint(9, 37)} (KHTML, like Gecko) Chrome/{random.randint(100, 121)}.0.0.0 Safari/{random.randint(512, 538)}.{random.randint(9, 37)}"
        }
        print(f'随机生成了 header {headers}')
        return headers

    def _pxvid_get(self):
        '''获取临时令牌'''
        # url 混淆服务器的路径 要么重定向，要么就强行给资源，总之我请求发出去了，没有404就表示没问题，爱给不给，随心所欲
        url = f"{hosturl}ip/{random.randint(1, 10000)}{str(time.time())}{str(random.randint(1, 10000))}{str(time.time())}{str(random.randint(1, 10000))}"
        print(url)
        # 使用 head 请求，不请求正文资源，只请求响应头，【正文没有用，都是一些我们不需要的东西，而且还浪费流量】
        # verify 关闭证书验证，可以更快的获取到响应头，虽然会返回404或其他状态码，但丝毫不影响我们参数获取
        response = requests.head(url, headers=self.Headers(), verify=False)
        # 从 cookie 里获取 _pxhd 数据信息，然会提取出 _pxvid 参数
        _pxvid = response.cookies.get('_pxhd').split(':')[-1]
        print(f'获取到令牌 {_pxvid}')
        return _pxvid

    def requests_data(self):
        '''请求公开数据'''
        Cookie = {
            "_pxvid": self._pxvid_get(),
        }
        print('等待20秒后')
        time.sleep(10)
        print('开始请求')
        response = requests.get(f'https://www.walmart.com/ip/JONPONY-Manual-Recliner-Chair-Heat-Therapy-Massage-Function-Heavy-Duty-Reclining-Mechanism-Chair-Elderly-Single-Rocker-Sofa-Cup-Holders-Bedroom-Home/5055386511', headers=self.Headers(), cookies=Cookie)
        print(response.text)

main().requests_data()
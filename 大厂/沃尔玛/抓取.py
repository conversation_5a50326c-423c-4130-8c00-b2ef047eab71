import logging
import random
import time
from datetime import datetime
import jmespath
import pymysql
import redis
import requests
import json
import re
from retrying import retry
from lxml import html
from fake_useragent import UserAgent

# 配置日志格式，包括时间信息
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',  # 包含时间、日志级别和日志消息
    datefmt='%Y-%m-%d %H:%M:%S',  # 时间格式
)

# Redis 连接配置
# 设置Redis服务器连接信息
host = '*************'
port = 6379
db = 2
password = 'TezgyWhIxYHkBfci'

# 创建Redis客户端实例并设置账号和密码
redis_client = redis.StrictRedis(
    host=host,
    port=port,
    db=db,
    password=password
)

# 初始化重试计数器和user-agent
retry_count = 0
current_user_agent = None
error_412_count = 0  # 412错误计数器
max_412_errors = 2  # 最大412错误次数


def normalize_walmart_url(url):
    """
    标准化沃尔玛URL格式

    参数:
        url (str): 原始URL

    返回:
        str: 标准化后的URL
    """
    if not url or not isinstance(url, str):
        raise ValueError("URL不能为空且必须是字符串")

    # 去除首尾空格
    url = url.strip()

    # 检查URL是否为空
    if not url:
        raise ValueError("URL不能为空")

    # 如果没有协议头，添加https://
    if not url.startswith(('http://', 'https://')):
        if url.startswith('www.walmart.com') or url.startswith('walmart.com'):
            url = 'https://' + url.lstrip('www.')
        elif url.startswith('//'):
            # 处理 //walmart.com 格式
            url = 'https:' + url
        else:
            # 如果看起来像沃尔玛URL路径，添加完整域名
            if url.startswith('/ip/') or '/ip/' in url:
                if not url.startswith('/'):
                    url = '/' + url
                url = 'https://www.walmart.com' + url
            else:
                # 其他情况，假设是完整域名但缺少协议
                url = 'https://' + url

    # 确保是沃尔玛域名
    if 'walmart.com' not in url:
        logging.warning(f"URL不是沃尔玛域名: {url}")

    # 验证URL格式
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        if not parsed.netloc:
            raise ValueError(f"无效的URL格式: {url}")
    except Exception as e:
        logging.error(f"URL解析失败: {e}")
        raise ValueError(f"URL格式错误: {url}")

    return url


def safe_get_nested_value(data, path, default=None):
    """
    安全地从嵌套字典中获取值，支持数组索引访问

    参数:
        data: 要搜索的字典
        path: 路径字符串，如 'props.pageProps.initialData.data.product.fulfillmentOptions[0].availableQuantity'
        default: 如果路径不存在时返回的默认值

    返回:
        提取的值或默认值
    """
    try:
        # 将路径拆分为键和索引
        import re
        # 分割路径，处理数组访问
        parts = re.split(r'\.|\[|\]', path)
        parts = [part for part in parts if part]  # 移除空字符串

        current = data
        i = 0
        while i < len(parts):
            part = parts[i]

            # 检查是否是数组索引
            if part.isdigit():
                index = int(part)
                if isinstance(current, list) and 0 <= index < len(current):
                    current = current[index]
                else:
                    return default
            else:
                # 普通字典访问
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return default
            i += 1

        return current
    except (TypeError, KeyError, IndexError, AttributeError, ValueError):
        return default




def reset_ua_fail_count():
    """
    重置User-Agent失败计数器（请求成功时调用）
    """
    global ua_fail_count
    ua_fail_count = 0


def increment_ua_fail_count():
    """
    增加User-Agent失败计数器（请求失败时调用）
    """
    global ua_fail_count
    ua_fail_count += 1
    logging.info(f"User-Agent失败计数: {ua_fail_count}")


def get_database_connection():
    """
    获取数据库连接

    返回:
        pymysql.Connection: 数据库连接对象
    """
    return pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,
        charset='utf8mb4'
    )


def extract_walmart_item_id(url):
    """
    从沃尔玛URL中提取商品ID

    参数:
        url (str): 沃尔玛商品URL

    返回:
        str or None: 提取的商品ID，如果提取失败返回None
    """
    url_match = re.search(r'/ip/.*?/(\d+)', url)
    return url_match.group(1) if url_match else None


def delete_item_records(connection, walmart_item_id):
    """
    从数据库中删除指定商品ID的记录

    参数:
        connection: 数据库连接对象
        walmart_item_id (str): 要删除的商品ID

    返回:
        tuple: (删除的sgm_walmart_item行数, 删除的sgm_walmart_item_subscribe行数)
    """
    try:
        with connection.cursor() as cursor:
            # 删除sgm_walmart_item表中的记录
            delete_sql_item = "DELETE FROM sgm_walmart_item WHERE walmart_item_id = %s"
            deleted_rows_item = cursor.execute(delete_sql_item, (walmart_item_id,))

            # 删除sgm_walmart_item_subscribe表中的记录
            delete_sql_subscribe = "DELETE FROM sgm_walmart_item_subscribe WHERE walmart_item_id = %s"
            deleted_rows_subscribe = cursor.execute(delete_sql_subscribe, (walmart_item_id,))

            connection.commit()
            return deleted_rows_item, deleted_rows_subscribe

    except pymysql.Error as db_error:
        logging.error(f"删除数据库记录失败 - walmart_item_id: {walmart_item_id}, 错误: {db_error}")
        raise
    except Exception as e:
        logging.error(f"删除操作异常 - walmart_item_id: {walmart_item_id}, 错误: {e}")
        raise


def handle_404_error(url, connection):
    """
    处理404错误，从URL中提取商品ID并删除数据库中的相关记录

    参数:
        url (str): 404错误的URL
        connection: 数据库连接对象
    """
    logging.info(f"链接信息不存在:{url}")

    # 从URL中提取walmart_item_id
    walmart_item_id = extract_walmart_item_id(url)

    if walmart_item_id:
        logging.info(f"从URL提取到的商品ID: {walmart_item_id}")

        try:
            deleted_rows_item, deleted_rows_subscribe = delete_item_records(connection, walmart_item_id)
            logging.info(
                f"已删除数据库记录 - walmart_item_id: {walmart_item_id}, sgm_walmart_item表删除{deleted_rows_item}行, sgm_walmart_item_subscribe表删除{deleted_rows_subscribe}行")
        except Exception as e:
            logging.error(f"删除记录时发生错误: {e}")
    else:
        logging.warning(f"无法从URL中提取商品ID: {url}")





def insert_item_data(connection, item_data):
    """
    将商品数据插入数据库

    参数:
        connection: 数据库连接对象
        item_data (dict): 包含商品信息的字典
    """
    try:
        with connection.cursor() as cursor:
            sql = """
               INSERT INTO sgm_walmart_item (available_quantity, walmart_item_id, price,rating, total_media_count,total_review_count,reviews_with_text_count, percentage_five_count, percentage_four_count, percentage_three_count, percentage_two_count, percentage_one_count,create_datetime,update_datetime,shipPrice) 
               VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s,%s,%s,%s,%s)
               """
            cursor.execute(sql, (
                item_data['availableQuantity'], item_data['itemId'], item_data['price'],
                item_data['rating'], item_data['total_media_count'], item_data['total_review_count'],
                item_data['reviews_with_text_count'], item_data['percentage_five_count'],
                item_data['percentage_four_count'], item_data['percentage_three_count'],
                item_data['percentage_two_count'], item_data['percentage_one_count'],
                item_data['current_time'], item_data['current_time'], item_data['shipPrice']
            ))

            logging.info(f"数据成功插入数据库 - ItemId: {item_data['itemId']}")

    except pymysql.Error as db_error:
        logging.error(f"数据库插入失败 - ItemId: {item_data['itemId']}, 错误: {db_error}")
        raise
    except Exception as e:
        logging.error(f"数据库操作异常 - ItemId: {item_data['itemId']}, 错误: {e}")
        raise


def safe_get_array_item(data, index, default=None):
    """
    安全地从数组中获取指定索引的元素

    参数:
        data: 要搜索的数组
        index: 数组索引
        default: 如果索引不存在时返回的默认值

    返回:
        提取的值或默认值
    """
    try:
        if isinstance(data, list) and 0 <= index < len(data):
            return data[index]
        return default
    except (TypeError, IndexError):
        return default

def generate_edge_ua():
    """生成简洁的 Microsoft Edge User-Agent 字符串"""
    # Windows 版本号范围
    windows_versions = [
        "10.0",  # Windows 10
        "11.0"  # Windows 11
    ]

    # Chrome 和 Edge 版本号范围 (基于实际版本范围)
    chrome_major = random.randint(120, 140)  # 主版本号 (120-140 是常见范围)
    chrome_minor = random.randint(0, 99)  # 次要版本号
    chrome_build = random.randint(1000, 9999)  # 构建号

    # 构建版本字符串 - 使用更常见的格式
    chrome_version = f"{chrome_major}.{chrome_minor}.{chrome_build}"
    edge_version = f"{chrome_major}.{chrome_minor}.{chrome_build}"

    # 组装简洁的 User-Agent 字符串
    ua = (
        f"Mozilla/5.0 (Windows NT {random.choice(windows_versions)}; Win64; x64) "
        f"AppleWebKit/537.36 (KHTML, like Gecko) "
        f"Chrome/{chrome_version} Safari/537.36 "
        f"Edg/{edge_version}"
    )

    return ua

@retry(stop_max_attempt_number=5, wait_fixed=6000)
def getData(url):
    """
    获取指定URL的页面数据并解析所需信息。

    参数:
        url (str): 要请求的URL。
        retries (int): 重试次数。
        delay (int): 每次重试之间的延迟时间（秒）。

    返回:
        None
    """
    global retry_count

    # 使用专门的URL处理函数标准化URL
    try:
        original_url = url
        url = normalize_walmart_url(url)
        if url != original_url:
            logging.info(f"URL已标准化: {original_url} -> {url}")
    except ValueError as e:
        logging.error(f"URL格式错误: {e}")
        return  # 跳过无效URL

    # 获取数据库连接
    connection = get_database_connection()

    try:
        # 预请求延迟，模拟人类行为
        time.sleep(random.uniform(1.5, 3.5))
        headers = {
            'accept': 'application/json',
            'accept-language': 'en-US',
            'content-type': 'application/json',
            'user-agent': generate_edge_ua(),
            'wm_mp': 'true',
            'wm_page_url': url,
        }
        # 发送请求（无代理模式）
        logging.debug("使用无代理模式请求")
        response = requests.get(url, headers=headers, timeout=30,allow_redirects=True)
        # 针对不同状态码的处理
        if response.status_code == 404:
            handle_404_error(url, connection)
            return
        elif response.status_code == 412:
            print(response.text)
            raise Exception(f"412 Precondition Failed - 需要更换请求策略")
        elif response.status_code == 403:
            # 403 Forbidden 处理
            logging.warning(f"遇到403错误，可能被反爬虫检测: {url}")
            raise Exception(f"403 Forbidden - 访问被拒绝，可能需要更换User-Agent")
        elif response.status_code == 429:
            # 429 Too Many Requests 处理
            logging.warning(f"遇到429错误，请求过于频繁: {url}")
            time.sleep(random.uniform(10, 20))  # 增加等待时间
            raise Exception(f"429 Too Many Requests - 请求过于频繁")
        elif response.status_code not in [200, 301, 302]:
            raise Exception(f"请求失败，状态码: {response.status_code}, 响应: {response.text[:200]}")

        # 检测验证页面
        tree = html.fromstring(response.text)
        verification_elements = tree.xpath('//*[contains(text(),"Activate and hold the button to confirm that")]')
        if verification_elements:
            logging.warning(f"检测到验证页面: {url}")
            raise Exception("检测到验证页面，需要人工干预")

        # 安全地查找并提取JSON数据
        script_matches = re.findall('''<script id="__NEXT_DATA__" type="application/json" .*?>(.*?)</script>''',
                                    response.text)

        if not script_matches:
            logging.warning(f"未找到__NEXT_DATA__脚本标签: {url}")
            return

        script_tag = script_matches[0]

        if script_tag:
            try:
                json_data = json.loads(script_tag)
            except json.JSONDecodeError as e:
                logging.error(f"JSON解析失败: {e}, URL: {url}")
                return
            # 安全地提取所有数据，设置默认值
            # 库存
            availableQuantity = safe_get_nested_value(json_data,
                                                      'props.pageProps.initialData.data.product.fulfillmentOptions[0].availableQuantity',
                                                      0)

            # 产品id - 这是关键字段，如果为空则跳过
            itemId = safe_get_nested_value(json_data,
                                           'props.pageProps.initialData.data.contentLayout.pageMetadata.pageContext.itemContext.itemId')
            if not itemId:
                logging.warning(f"无法获取产品ID，跳过此商品: {url}")
                return

            # 价格
            price = safe_get_nested_value(json_data,
                                          'props.pageProps.initialData.data.product.priceInfo.currentPrice.price', 0)
            shipPrice = safe_get_nested_value(json_data,
                                              "props.pageProps.initialData.data.product.fulfillmentOptions[0].speedDetails.fulfillmentPrice.price",
                                              0)

            # 标题
            title = safe_get_nested_value(json_data, 'props.pageProps.initialData.data.product.name', "")

            # 评分相关数据，设置默认值
            rating = safe_get_nested_value(json_data,
                                           'props.pageProps.initialData.data.reviews.roundedAverageOverallRating', 0)
            total_media_count = safe_get_nested_value(json_data,
                                                      'props.pageProps.initialData.data.reviews.totalMediaCount', 0)
            total_review_count = safe_get_nested_value(json_data,
                                                       'props.pageProps.initialData.data.reviews.totalReviewCount', 0)
            reviews_with_text_count = safe_get_nested_value(json_data,
                                                            'props.pageProps.initialData.data.reviews.reviewsWithTextCount',
                                                            0)
            percentage_five_count = safe_get_nested_value(json_data,
                                                          'props.pageProps.initialData.data.reviews.percentageFiveCount',
                                                          0)
            percentage_four_count = safe_get_nested_value(json_data,
                                                          'props.pageProps.initialData.data.reviews.percentageFourCount',
                                                          0)
            percentage_three_count = safe_get_nested_value(json_data,
                                                           'props.pageProps.initialData.data.reviews.percentageThreeCount',
                                                           0)
            percentage_two_count = safe_get_nested_value(json_data,
                                                         'props.pageProps.initialData.data.reviews.percentageTwoCount',
                                                         0)
            percentage_one_count = safe_get_nested_value(json_data,
                                                         'props.pageProps.initialData.data.reviews.percentageOneCount',
                                                         0)

            # 记录成功提取的数据用于调试
            logging.info(f"成功提取数据 - ItemId: {itemId}, 价格: {price}, 库存: {availableQuantity}, 评分: {rating}")

            # 链接
            walmart_item_url = url

            # 获取当前时间
            current_time = datetime.now()

            # 将当前时间格式化为字符串
            current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')

            # 将格式化的字符串解析回 datetime 对象
            current_time_obj = datetime.strptime(current_time_str, '%Y-%m-%d %H:%M:%S')

            # 准备商品数据字典
            item_data = {
                'availableQuantity': availableQuantity,
                'itemId': itemId,
                'price': price,
                'rating': rating,
                'total_media_count': total_media_count,
                'total_review_count': total_review_count,
                'reviews_with_text_count': reviews_with_text_count,
                'percentage_five_count': percentage_five_count,
                'percentage_four_count': percentage_four_count,
                'percentage_three_count': percentage_three_count,
                'percentage_two_count': percentage_two_count,
                'percentage_one_count': percentage_one_count,
                'current_time': current_time_obj,
                'shipPrice': shipPrice
            }

            # 插入数据库
            insert_item_data(connection, item_data)
    except Exception as e:
        retry_count = retry_count + 1
        logging.error(f"请求失败，重新尝试: {e}, 重试次数: {retry_count}")

        # 如果失败次数超过2次，增加延迟
        if retry_count > 2:
            logging.info("连续失败超过两次...")
            retry_count = 0
        raise
    finally:
        connection.close()


def get_info():
    logging.info("========程序开始执行（无代理模式）=====")

    # 记录开始时间
    start_time = time.time()
    # 获取数据库连接
    connection = get_database_connection()
    failed_urls = []  # 用于存储处理失败的 URL

    with connection.cursor() as cursor:
        # 查询数据库
        sql = "SELECT * FROM sgm_walmart_item_subscribe ORDER BY create_datetime DESC"
        cursor.execute(sql)
        # 获取查询结果
        results = cursor.fetchall()
        # # 处理查询结果
        for index, row in enumerate(results):
            url = row[7]
            logging.info(f"总数: {len(results)} 已经完成数量: {index + 1} 目前爬取商品URL: {url}")

            # 重置412错误计数器（每个新URL开始时重置）
            global error_412_count
            error_412_count = 0

            # 预先验证URL格式
            try:
                normalized_url = normalize_walmart_url(url)
                if normalized_url != url:
                    logging.info(f"数据库URL已标准化: {url} -> {normalized_url}")
                    url = normalized_url
            except ValueError as url_error:
                logging.error(f"数据库URL格式错误，跳过: {url}, 错误: {url_error}")
                failed_urls.append(url)
                continue

            try:
                getData(url)  # 尝试调用 getData(url)
            except Exception as e:
                logging.error(f"处理商品 {url} 时发生错误: {e}")
                failed_urls.append(url)  # 记录失败的 URL
    # 记录失败的 URL 列表
    if failed_urls:
        logging.info(f"处理失败的URL数量: {len(failed_urls)}，列表如下:")
        for failed_url in failed_urls:
            logging.info(f"失败的URL: {failed_url}")
    else:
        logging.info("所有URL处理成功，无失败的URL")

    # 记录结束时间并计算总耗时
    end_time = time.time()
    total_time = end_time - start_time  # 计算总时间，单位为秒
    # 将总时间格式化为小时、分钟、秒
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)

    logging.info(f"========程序执行结束，耗时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒 ========")


if __name__ == '__main__':
    try:
        get_info()
    except KeyboardInterrupt:
        logging.info("程序被用户中断")
    except Exception as e:
        logging.error(f"程序执行异常: {e}")
        raise

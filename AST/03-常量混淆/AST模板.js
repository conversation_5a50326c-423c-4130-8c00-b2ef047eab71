const fs = require('fs');
// js 转 ast
const parser = require("@babel/parser");
//遍历所有节点的函数
const traverse = require("@babel/traverse").default;
//判断节点类型，构造节点
const types = require("@babel/types");
// ast 转 js
const generator = require("@babel/generator").default;
// 混淆js代码文件
const encode_file = "./encode.js"
// 发混淆js代码文件
const decode_file = "./decode.js"

// 读取混淆后的代码
const encode_code = fs.readFileSync(encode_file, {encoding: "utf-8"});

// 将js代码转换为ast
const ast = parser.parse(encode_code);

// 定义全局变量，这样eval可以访问到
const data = [1, 2, 3, 4, 5, 6, 7, 8, 9];
const fnTable = [(a, b) => a + b, (a, b) => a - b, (a, b) => a * b, (a, b) => a / b];
function process(a, b, op) {
  return fnTable[op](a, b);
}

// 处理混淆代码
const visitor = {
    VariableDeclarator(path) {
        let node = path.node
        if (types.isCallExpression(node.init) && node.init.callee.name === 'process') {
            // 计算表达式的值
            let result = eval(generator(node.init).code);
            console.log(`${generator(node.init).code} = ${result}`);
            // 将表达式替换为计算出的常量值
            node.init = types.numericLiteral(result);
            eval(path.toString());
        }
    }
} 

// 遍历所有节点
traverse(ast, visitor)

// 将处理后的ast转换为js代码(反混淆后的代码
let {code} = generator(ast)
// 保存代码
fs.writeFileSync(decode_file, code,()=>{})
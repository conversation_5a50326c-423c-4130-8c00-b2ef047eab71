const fs = require('fs');
// js 转 ast
const parser = require("@babel/parser");
//遍历所有节点的函数
const traverse = require("@babel/traverse").default;
//判断节点类型，构造节点
const types = require("@babel/types");
// ast 转 js
const generator = require("@babel/generator").default;
// 混淆js代码文件
const encode_file = "./encode.js"
// 发混淆js代码文件
const decode_file = "./decode.js"

// 读取混淆后的代码
const encode_code = fs.readFileSync(encode_file, {encoding: "utf-8"});

// 将js代码转换为ast
const ast = parser.parse(encode_code);

// 通过检查`node.extra.raw`的值，我们可以确定这个数字字面量是以哪种编码类型表示的（如十六进制、八进制或二进制），然后根据需要将其转换为相应的十进制数值。
// 通过将`node.extra`设置为`undefined`，我们可以确保在后续的处理过程中，这个数字字面量被视为一个普通的十进制数，而不受其原始编码类型的干扰。
// 处理混淆代码
const visitor = {
    // 处理数字字面量 - 将十六进制、二进制、八进制转换为十进制
    NumericLiteral(path) {
        const node = path.node;
        // 检查是否为特殊进制的数字（通过extra.raw属性判断）
        if (node.extra && node.extra.raw) {
            const rawValue = node.extra.raw;
            if (rawValue.startsWith('0x') || rawValue.startsWith('0X') ||  // 十六进制
                rawValue.startsWith('0b') || rawValue.startsWith('0B') ||  // 二进制  
                rawValue.startsWith('0o') || rawValue.startsWith('0O')) {  // 八进制
                // 保持字符串值不变，但清除原始转义信息
                node.extra = undefined;
            }
        }
    },
    
    // 处理字符串字面量 - 将转义字符转换为正常字符串
    StringLiteral(path) {
        const node = path.node;
        // 检查原始字符串是否包含转义序列（通过extra.raw属性判断）
        if (node.extra && node.extra.raw) {
            const rawValue = node.extra.raw;
            if (rawValue.includes('\\x') || rawValue.includes('\\u')) {
                // 保持字符串值不变，但清除原始转义信息
                node.extra = undefined;
            }
        }
    }
}

// 遍历所有节点
traverse(ast, visitor);

// 将处理后的ast转换为js代码(反混淆后的;代码
let {code} = generator(ast)
// 保存代码
fs.writeFileSync(decode_file, code,()=>{})
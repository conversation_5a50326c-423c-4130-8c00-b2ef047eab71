const fs = require('fs');
// js 转 ast
const parser = require("@babel/parser");
//遍历所有节点的函数
const traverse = require("@babel/traverse").default;
//判断节点类型，构造节点
const types = require("@babel/types");
// ast 转 js
const generator = require("@babel/generator").default;
// 混淆js代码文件
const encode_file = "./encode.js"
// 发混淆js代码文件
const decode_file = "./decode.js"

// 读取混淆后的代码
const encode_code = fs.readFileSync(encode_file, {encoding: "utf-8"});

// 将js代码转换为ast
const ast = parser.parse(encode_code);

// 处理混淆代码
const visitor = {
    // 处理二元表达式进行常量折叠
    BinaryExpression(path) {
        const { node } = path;
        const { left, right, operator } = node;
        
        // 只处理加法运算
        if (operator === '+') {
            // 检查左右操作数是否都是字面量
            if (types.isLiteral(left) && types.isLiteral(right)) {
                let result;
                
                // 获取字面量的值
                const leftValue = left.value;
                const rightValue = right.value;
                
                // 进行加法运算
                result = leftValue + rightValue;
                
                // 创建新的字面量节点替换原来的二元表达式
                if (typeof result === 'string') {
                    path.replaceWith(types.stringLiteral(result));
                } else if (typeof result === 'number') {
                    path.replaceWith(types.numericLiteral(result));
                }
            }
        }
    }
} 

// 遍历所有节点
traverse(ast, visitor);

// 将处理后的ast转换为js代码(反混淆后的;代码
let {code} = generator(ast)
// 保存代码
fs.writeFileSync(decode_file, code,()=>{})
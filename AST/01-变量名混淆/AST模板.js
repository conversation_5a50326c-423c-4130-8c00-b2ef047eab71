const fs = require('fs');
// js 转 ast
const parser = require("@babel/parser");
//遍历所有节点的函数
const traverse = require("@babel/traverse").default;
//判断节点类型，构造节点
const types = require("@babel/types");
// ast 转 js
const generator = require("@babel/generator").default;


// 混淆js代码文件
const encode_file = "./encode.js"
// 发混淆js代码文件
const decode_file = "./decode.js"
// 配置文件（可选）
const config_file = "./obfuscate_config.json"

// 默认混淆配置
let config = {
    obfuscateFunctionNames: false, // 是否混淆函数声明的名称
    obfuscateObjectProperties: false, // 是否混淆对象属性名
    preserveLogMethods: true, // 是否保留日志方法的可读性
    logObjects: ["console", "logger", "log", "logging"], // 日志对象名称列表
    preservedIdentifiers: [] // 用户自定义需要保留的标识符列表
};

// 尝试加载配置文件
try {
    if (fs.existsSync(config_file)) {
        const userConfig = JSON.parse(fs.readFileSync(config_file, 'utf-8'));
        config = { ...config, ...userConfig };
        console.log("已加载配置文件:", config_file);
    }
} catch (error) {
    console.error("加载配置文件失败:", error.message);
}

// 日志相关方法列表
const logMethods = [
    // 基础日志方法
    "log", "info", "warn", "error", "debug", "trace", "dir", "dirxml", "table", 
    "time", "timeEnd", "timeLog", "assert", "count", "countReset", "group", 
    "groupCollapsed", "groupEnd", "profile", "profileEnd", "timeStamp", "clear",
    // Winston日志库方法
    "silly", "verbose", "http",
    // Log4js日志库方法
    "fatal", "mark", "all"
];

let js_code = fs.readFileSync(encode_file, "utf-8");
// 代码转AST
let ast = parser.parse(js_code)

// JavaScript内置函数、关键字和保留字列表
const builtIns = [
    // 全局对象
    "Object", "Array", "Function", "String", "Boolean", "Number", "Date", "RegExp", "Error", 
    "Math", "JSON", "Promise", "Map", "Set", "WeakMap", "WeakSet", "Symbol", "Proxy", "Reflect",
    // 常用方法
    "parseInt", "parseFloat", "isNaN", "isFinite", "decodeURI", "decodeURIComponent", 
    "encodeURI", "encodeURIComponent", "eval", "setTimeout", "setInterval", "clearTimeout", 
    "clearInterval", "console", "alert", "confirm", "prompt",
    // ES6+
    "ArrayBuffer", "DataView", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", 
    "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "BigInt64Array", 
    "BigUint64Array",
    // 关键字
    "break", "case", "catch", "class", "const", "continue", "debugger", "default", "delete", 
    "do", "else", "export", "extends", "finally", "for", "function", "if", "import", "in", 
    "instanceof", "new", "return", "super", "switch", "this", "throw", "try", "typeof", 
    "var", "void", "while", "with", "yield",
    // 保留字
    "enum", "implements", "interface", "let", "package", "private", "protected", "public", 
    "static", "await", "abstract", "boolean", "byte", "char", "double", "final", "float", 
    "goto", "int", "long", "native", "short", "synchronized", "throws", "transient", "volatile",
    // DOM API
    "document", "window", "navigator", "location", "history", "screen", "localStorage", 
    "sessionStorage", "fetch", "XMLHttpRequest", "addEventListener", "removeEventListener", 
    "querySelector", "querySelectorAll", "getElementById", "getElementsByClassName", 
    "getElementsByTagName", "createElement", "createTextNode", "appendChild", "removeChild", 
    "replaceChild", "insertBefore", "getAttribute", "setAttribute", "removeAttribute", 
    "classList", "innerHTML", "innerText", "textContent", "style", "event", "target",
    // Node.js 内置模块
    "fs", "path", "http", "https", "url", "querystring", "crypto", "zlib", "util", "stream", 
    "buffer", "events", "os", "child_process", "cluster", "dgram", "dns", "net", "readline", 
    "repl", "tls", "process", "require", "module", "exports", "__dirname", "__filename", 
    "global",
    // 常见日志库
    "winston", "log4js", "bunyan", "pino", "loglevel"
];

let map = {};
// 处理混淆代码
const visitor = {
    Identifier(path){
        let name = path.node.name, rename;
        
        // 如果是JavaScript内置函数、关键字或保留字，则跳过
        if(builtIns.includes(name)){
            return;
        }
        
        // 如果是用户自定义需要保留的标识符，则跳过
        if(config.preservedIdentifiers.includes(name)){
            return;
        }
        
        // 处理日志方法
        if(config.preserveLogMethods){
            // 检查是否是日志方法
            if(logMethods.includes(name)){
                // 检查是否是日志对象的属性
                if(path.parent.type === "MemberExpression" && 
                   path.parent.property === path.node &&
                   path.parent.object.type === "Identifier" && 
                   config.logObjects.includes(path.parent.object.name)){
                    return; // 跳过日志方法
                }
            }
            
            // 检查是否是日志对象
            if(config.logObjects.includes(name)){
                return; // 跳过日志对象
            }
        }
        
        // 跳过属性名（根据配置决定）
        if(!config.obfuscateObjectProperties && path.parent.type === "ObjectProperty" && path.parent.key === path.node){
            return;
        }
        
        // 跳过导入导出的标识符
        if(path.parent.type === "ImportSpecifier" || 
           path.parent.type === "ImportDefaultSpecifier" ||
           path.parent.type === "ImportNamespaceSpecifier" ||
           path.parent.type === "ExportSpecifier"){
            return;
        }
        
        // 跳过函数声明的名称（根据配置决定）
        if(!config.obfuscateFunctionNames && path.parent.type === "FunctionDeclaration" && path.parent.id === path.node){
            return;
        }
        
        if(map[name]){
            rename = map[name];
        }else{
            rename = 'OxHD' + parseInt(Math.random() * 1000000).toString(16);
            map[name] = rename;
        }
        path.node.name = rename;
    }
}


// 遍历所有节点
traverse(ast, visitor)


// 将处理后的ast转换为js代码(反混淆后的代码
let {code} = generator(ast)
// 保存代码
fs.writeFileSync(decode_file, code,()=>{})

// 保存映射表（可选，用于调试）
if(config.saveMap){
    fs.writeFileSync('./obfuscate_map.json', JSON.stringify(map, null, 2), 'utf-8');
}

const fs = require('fs');
// js 转 ast
const parser = require("@babel/parser");
//遍历所有节点的函数
const traverse = require("@babel/traverse").default;
//判断节点类型，构造节点
const types = require("@babel/types");
// ast 转 js
const generator = require("@babel/generator").default;
// 混淆js代码文件
const encode_file = "./encode.js"
// 发混淆js代码文件
const decode_file = "./decode.js"

// 读取混淆后的代码
const encode_code = fs.readFileSync(encode_file, {encoding: "utf-8"});

// 将js代码转换为ast
const ast = parser.parse(encode_code);

// 处理混淆代码
const visitor = {
    // 处理函数调用表达式
    CallExpression(path) {
        // 检查是否是对成员表达式的调用
        if (types.isMemberExpression(path.node.callee)) {
            const memberExpr = path.node.callee;
            
            // 检查是否是字符串字面量['split'](',')的模式
            if (types.isStringLiteral(memberExpr.object) && 
                types.isStringLiteral(memberExpr.property) && 
                memberExpr.property.value === 'split' &&
                memberExpr.computed === true && // 使用了[]语法
                path.node.arguments.length === 1 &&
                types.isStringLiteral(path.node.arguments[0])) {
                
                // 获取字符串内容和分隔符
                const stringValue = memberExpr.object.value;
                const separator = path.node.arguments[0].value;
                
                // 执行split操作
                const resultArray = stringValue.split(separator);
                
                // 创建数组字面量节点
                const arrayElements = resultArray.map(item => types.stringLiteral(item));
                const arrayLiteral = types.arrayExpression(arrayElements);
                
                // 替换整个调用表达式
                path.replaceWith(arrayLiteral);
            }
        }
    }
}

// 遍历所有节点
traverse(ast, visitor);

// 将处理后的ast转换为js代码(反混淆后的;代码
let {code} = generator(ast)
// 保存代码
fs.writeFileSync(decode_file, code,()=>{})
const fs = require('fs');
// js 转 ast
const parser = require("@babel/parser");
//遍历所有节点的函数
const traverse = require("@babel/traverse").default;
//判断节点类型，构造节点
const types = require("@babel/types");
// ast 转 js
const generator = require("@babel/generator").default;
// 混淆js代码文件
const encode_file = "./encode.js"
// 发混淆js代码文件
const decode_file = "./decode.js"

// 读取混淆后的代码
const encode_code = fs.readFileSync(encode_file, {encoding: "utf-8"});

// 将js代码转换为ast
const ast = parser.parse(encode_code);

// 配置选项
const config = {
    saveMap: false
};

// 处理三元表达式
function processAssignmentWithConditional(path) {
    const { left, operator, right } = path.node;

    // 获取三元的三个部分
    const { test, consequent, alternate } = right;

    // 构建新的赋值表达式节点：a = 2 和 a = 3
    const newConsequent = types.assignmentExpression(
        operator,
        left,
        consequent
    );

    const newAlternate = types.assignmentExpression(
        operator,
        left,
        alternate
    );

    // 创建新的三元表达式节点
    const newConditional = types.conditionalExpression(test,newConsequent,newAlternate);

    // 替换原来的表达式语句节点为新创建的三元表达式节点
    path.replaceWith(newConditional);
}


// 处理混淆代码
const visitor = {
    // 处理变量声明中的三元表达式
    AssignmentExpression(path) {
        // 检查是否是三元表达式
        if(types.isConditionalExpression(path.node.right)){
            processAssignmentWithConditional(path);
        }
    },
    
    // 访问三元表达式节点
    ExpressionStatement(path) {
        // 检查是否是三元表达式
        if (path.node.expression && path.node.expression.type === 'ConditionalExpression') {
            const expression = path.node.expression;
            // test: 条件部分，即 a ? ... 中的 a
            // consequent: 条件为真时执行的表达式（b）
            // alternate: 条件为假时执行的表达式（c）
            const { test, consequent, alternate } = expression;
            
            // 创建if语句节点
            const ifStatement = types.ifStatement(
                test,
                types.blockStatement([
                    types.expressionStatement(consequent)
                ]),
                types.blockStatement([
                    types.expressionStatement(alternate)
                ])
            );
            
            // 替换原来的表达式语句节点为新创建的if语句节点
            path.replaceWith(ifStatement);
        }
    }
}


// 遍历所有节点
traverse(ast, visitor)


// 将处理后的ast转换为js代码(反混淆后的代码
let {code} = generator(ast)
// 保存代码
fs.writeFileSync(decode_file, code,()=>{})
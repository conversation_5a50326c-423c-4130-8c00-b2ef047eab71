const fs = require('fs');
// js 转 ast
const parser = require("@babel/parser");
//遍历所有节点的函数
const traverse = require("@babel/traverse").default;
//判断节点类型，构造节点
const types = require("@babel/types");
// ast 转 js
const generator = require("@babel/generator").default;


// 混淆js代码文件
const encode_file = "./encode.js"
// 发混淆js代码文件
const decode_file = "./decode.js"

let js_code = fs.readFileSync(encode_file, "utf-8");
// 代码转AST
let ast = parser.parse(js_code)

// 处理


// 遍历所有节点
traverse(ast, {
    enter(path) {
        console.log(path.node.type);
    }
})


// 将处理后的ast转换为js代码(反混淆后的代码
let {code} = generator(ast)
// 保存代码
fs.writeFileSync(decode_file, code,()=>{})

const fs = require('fs');
// js 转 ast
const parser = require("@babel/parser");
//遍历所有节点的函数
const traverse = require("@babel/traverse").default;
//判断节点类型，构造节点
const types = require("@babel/types");
// ast 转 js
const generator = require("@babel/generator").default;
// 混淆js代码文件
const encode_file = "./encode.js"
// 发混淆js代码文件
const decode_file = "./decode.js"

// 读取混淆后的代码
const encode_code = fs.readFileSync(encode_file, {encoding: "utf-8"});

// 将js代码转换为ast
const ast = parser.parse(encode_code);

// 处理混淆代码
const visitor = {
    // FunctionDeclaration(path){
    //     let node = path.node
    //     let params_name = node.params[0].name
        
    //     // 替换函数体内的参数使用
    //     path.traverse({
    //         Identifier(path){
    //             if(path.node.name === params_name){
    //                 console.log(path+'')
    //                 path.node.name = 'a'
    //             }
    //         }
    //     })
    // }
    // enter(path){
    //     if(types.isIdentifier(path.node,{"name":"a"})){
    //         path.node.name = "b";
    //     }
    // }
    VariableDeclarator(path){
        // 使用替换的方式来实现
        path.get("init").replaceWith(types.numericLiteral(211312))
        console.log(path+'')    
    }
} 

// 遍历所有节点
traverse(ast, visitor)

// 将处理后的ast转换为js代码(反混淆后的代码
let {code} = generator(ast)
// 保存代码
fs.writeFileSync(decode_file, code,()=>{})